"""
自定义异常类
"""

from typing import Any, Dict, Optional


class Text2SQLException(Exception):
    """Text2SQL基础异常"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DatabaseException(Text2SQLException):
    """数据库相关异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details,
        )





class SQLGenerationException(Text2SQLException):
    """SQL生成异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="SQL_GENERATION_ERROR",
            details=details,
        )


class ValidationException(Text2SQLException):
    """验证异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details,
        )


class IntentClassificationException(Text2SQLException):
    """意图分类异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="INTENT_CLASSIFICATION_ERROR",
            details=details,
        )


class QualityEvaluationException(Text2SQLException):
    """质量评估异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="QUALITY_EVALUATION_ERROR",
            details=details,
        )
