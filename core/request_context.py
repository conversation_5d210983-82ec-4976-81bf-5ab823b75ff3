"""
请求上下文中间件：注入 request_id、用户信息等到日志上下文
"""

import uuid
from typing import Callable

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
import structlog


class RequestContextMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))

        # 绑定到 structlog 上下文
        logger = structlog.get_logger(__name__)
        structlog.contextvars.bind_contextvars(
            request_id=request_id,
            path=request.url.path,
            method=request.method,
        )

        try:
            response = await call_next(request)
        finally:
            # 清理上下文，避免泄漏
            structlog.contextvars.clear_contextvars()

        # 回传 request id
        response.headers.setdefault("X-Request-ID", request_id)
        return response


