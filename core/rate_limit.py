"""
轻量级限流依赖（内存版）。生产建议接入网关或 Redis。
"""

import time
from collections import defaultdict, deque
from typing import Deque, Dict, Tuple

from fastapi import Depends, HTTPException, Request, status


class InMemoryRateLimiter:
    def __init__(self, max_requests: int, window_seconds: int):
        self.max_requests = max_requests
        self.window = window_seconds
        self.ip_to_timestamps: Dict[str, Deque[float]] = defaultdict(deque)

    def check(self, key: str) -> None:
        now = time.time()
        q = self.ip_to_timestamps[key]
        # 清理窗口外
        while q and now - q[0] > self.window:
            q.popleft()
        if len(q) >= self.max_requests:
            raise HTTPException(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail="Too many requests")
        q.append(now)


def limit_requests(max_requests: int, window_seconds: int):
    limiter = InMemoryRateLimiter(max_requests, window_seconds)

    def dependency(request: Request):
        # 以 IP 作为键；也可选用用户ID（需依赖鉴权）
        client_ip = request.client.host if request.client else "unknown"
        limiter.check(client_ip)

    return dependency


