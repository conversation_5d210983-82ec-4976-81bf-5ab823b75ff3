"""
Redis 客户端与自检
"""

from __future__ import annotations

from typing import Optional

import redis
from redis import Redis
from redis.exceptions import RedisError

from config import settings
from core.logging import get_logger

logger = get_logger(__name__)


_redis_client: Optional[Redis] = None


def _create_client() -> Redis:
    """基于配置创建 Redis 客户端（懒加载）。"""
    url = settings.redis.connection_url
    client = redis.from_url(
        url,
        decode_responses=True,
        health_check_interval=30,
    )
    return client  # type: ignore[return-value]


def get_redis() -> Redis:
    """获取全局 Redis 客户端实例。"""
    global _redis_client
    if _redis_client is None:
        try:
            _redis_client = _create_client()
            logger.info("Redis 客户端初始化成功")
        except Exception as exc:  # pragma: no cover - 启动阶段日志
            logger.error("Redis 客户端初始化失败", error=str(exc))
            raise
    return _redis_client


def test_connection() -> bool:
    """PING 自检，返回是否可用。"""
    try:
        client = get_redis()
        pong = client.ping()
        if pong:
            logger.info("Redis 连接测试成功")
            return True
        logger.warning("Redis 连接测试失败: PING 未返回 True")
        return False
    except RedisError as exc:  # pragma: no cover - 启动阶段日志
        logger.warning("Redis 连接测试异常", error=str(exc))
        return False


def close_redis() -> None:
    """关闭全局 Redis 连接。"""
    global _redis_client
    if _redis_client is not None:
        try:
            _redis_client.close()
            logger.debug("Redis 连接已关闭")
        finally:
            _redis_client = None

