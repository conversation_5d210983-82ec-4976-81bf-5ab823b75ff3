"""
数据库连接管理
"""

from contextlib import contextmanager
from typing import Any, Dict, Generator, List, Optional

import mysql.connector
from mysql.connector import Error
from mysql.connector.connection import MySQLConnection
from mysql.connector.pooling import MySQLConnectionPool, PooledMySQLConnection
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

from config import settings
from core.exceptions import DatabaseException
from core.logging import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._pool: Optional[MySQLConnectionPool] = None
        self._initialize_pool()
    
    def _initialize_pool(self) -> None:
        """初始化连接池"""
        try:
            pool_config = {
                'pool_name': 'text2sql_pool',
                'pool_size': 10,
                'pool_reset_session': True,
                'host': settings.database.host,
                'port': settings.database.port,
                'user': settings.database.user,
                'password': settings.database.password,
                'database': settings.database.database,
                'charset': settings.database.charset,
                'autocommit': True,
                'raise_on_warnings': True,
            }
            
            self._pool = MySQLConnectionPool(**pool_config)
            logger.info("数据库连接池初始化成功", pool_size=10)
            
        except Error as e:
            logger.error("数据库连接池初始化失败", error=str(e))
            raise DatabaseException(f"数��库连接池初始化失败: {e}")
    
    @contextmanager
    def get_connection(self) -> Generator[PooledMySQLConnection, None, None]:
        """获取数据库连接（上下文管理器）"""
        if not self._pool:
            raise DatabaseException("数据库连接池未初始化")
        connection = None
        try:
            connection = self._pool.get_connection()
            logger.debug("获取数据库连接成功")
            yield connection
            
        except Error as e:
            logger.error("数据库连接错误", error=str(e))
            if connection:
                connection.rollback()
            raise DatabaseException(f"数据库连接错误: {e}")
            
        finally:
            if connection and connection.is_connected():
                connection.close()
                logger.debug("数据库连接已关闭")
    
    def execute_query(self, sql: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(sql, params)
                results = cursor.fetchall()
                cursor.close()
                
                logger.info(
                    "SQL查询执行成功",
                    sql=sql[:100] + "..." if len(sql) > 100 else sql,
                    row_count=len(results)
                )
                
                return results
                
        except Error as e:
            logger.error("SQL查询执行失败", sql=sql, error=str(e))
            raise DatabaseException(f"SQL查询执行失败: {e}")
    
    def execute_update(self, sql: str, params: tuple = ()) -> int:
        """执行更新SQL"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(sql, params)
                affected_rows = cursor.rowcount
                cursor.close()
                
                logger.info(
                    "SQL更新执行成功",
                    sql=sql[:100] + "..." if len(sql) > 100 else sql,
                    affected_rows=affected_rows
                )
                
                return affected_rows
                
        except Error as e:
            logger.error("SQL更新执行失败", sql=sql, error=str(e))
            raise DatabaseException(f"SQL更新执行失败: {e}")
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                
                if result:
                    logger.info("数据库连接测试成功")
                    return result[0] == 1
                return False
                
        except Exception as e:
            logger.error("数据库连接测试失败", error=str(e))
            return False
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        sql = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
        ORDER BY ORDINAL_POSITION
        """
        
        return self.execute_query(sql, (settings.database.database, table_name))
    
    def get_all_tables(self) -> List[str]:
        """获取所有表名"""
        sql = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = %s AND TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """
        
        results = self.execute_query(sql, (settings.database.database,))
        return [row['TABLE_NAME'] for row in results]


# 全局数据库管理器实例
db_manager = DatabaseManager()


# ==================== SQLAlchemy ORM 支持 ====================

# 创建SQLAlchemy引擎
engine = create_engine(
    settings.database.connection_string,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=False  # 禁用SQL查询日志以减少日志输出
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """获取数据库会话（依赖注入）"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
