"""
对话记忆管理
"""

from typing import List

from core.exceptions import DatabaseException
from core.logging import get_logger
from database.connection import db_manager

logger = get_logger(__name__)


class MemoryManager:
    """对话记忆管理器"""
    
    def __init__(self):
        self.db = db_manager
    
    def fetch_memory(self, user_id: str, conversation_id: str) -> List[str]:
        """获取对话记忆"""
        try:
            # Step 1: 查找当前对话中最新一条记录的 massage_id 和 create_date
            sql_latest = """
                SELECT massage_id, MAX(create_date) as latest_date
                FROM tb_question_answer
                WHERE user_id = %s AND conversation_id = %s
            """

            latest_result = self.db.execute_query(sql_latest, (user_id, conversation_id))
            if not latest_result or not latest_result[0]['massage_id']:
                logger.debug("未找到对话记录", user_id=user_id, conversation_id=conversation_id)
                return []

            current_massage_id = latest_result[0]['massage_id']

            # Step 2: 查找前一个 massage_id
            sql_previous = """
                SELECT DISTINCT massage_id
                FROM tb_question_answer
                WHERE user_id = %s
                  AND conversation_id = %s
                  AND massage_id != %s
                ORDER BY create_date DESC
                LIMIT 1
            """

            previous_result = self.db.execute_query(
                sql_previous,
                (user_id, conversation_id, current_massage_id)
            )

            massage_ids = [current_massage_id]
            if previous_result:
                massage_ids.append(previous_result[0]['massage_id'])

            # Step 3: 拉取这些 massage_id 下的所有记录
            placeholders = ','.join(['%s'] * len(massage_ids))
            sql_records = f"""
                SELECT type, text
                FROM tb_question_answer
                WHERE user_id = %s
                  AND conversation_id = %s
                  AND massage_id IN ({placeholders})
                  AND type IN (1, 2)
                ORDER BY create_date ASC
            """

            params = [user_id, conversation_id] + massage_ids
            records = self.db.execute_query(sql_records, tuple(params))

            # 构建记忆文本
            memories = []
            for record in records:
                if record['type'] == 1:  # 问题
                    memories.append(f"问题：{record['text']}")
                elif record['type'] == 2:  # 回答
                    memories.append(f"回答：{record['text']}")

            logger.info(
                "获取对话记忆成功",
                user_id=user_id,
                conversation_id=conversation_id,
                memory_count=len(memories)
            )

            return memories

        except Exception as e:
            logger.error(
                "获取对话记忆失败",
                user_id=user_id,
                conversation_id=conversation_id,
                error=str(e)
            )
            raise DatabaseException(f"获取对话记忆失败: {e}")
    
    def save_question(
        self, 
        user_id: str, 
        conversation_id: str, 
        massage_id: str,
        question: str
    ) -> None:
        """保存用户问题"""
        try:
            sql = """
                INSERT INTO tb_question_answer 
                (user_id, conversation_id, massage_id, type, text, create_date)
                VALUES (%s, %s, %s, 1, %s, NOW())
            """
            
            self.db.execute_update(sql, (user_id, conversation_id, massage_id, question))
            
            logger.debug(
                "保存用户问题成功",
                user_id=user_id,
                conversation_id=conversation_id,
                massage_id=massage_id
            )
            
        except Exception as e:
            logger.error(
                "保存用户问题失败",
                user_id=user_id,
                conversation_id=conversation_id,
                error=str(e)
            )
            raise DatabaseException(f"保存用户问题失败: {e}")
    
    def save_answer(
        self, 
        user_id: str, 
        conversation_id: str, 
        massage_id: str,
        answer: str
    ) -> None:
        """保存系统回答"""
        try:
            sql = """
                INSERT INTO tb_question_answer 
                (user_id, conversation_id, massage_id, type, text, create_date)
                VALUES (%s, %s, %s, 2, %s, NOW())
            """
            
            self.db.execute_update(sql, (user_id, conversation_id, massage_id, answer))
            
            logger.debug(
                "保存系统回答成功",
                user_id=user_id,
                conversation_id=conversation_id,
                massage_id=massage_id
            )
            
        except Exception as e:
            logger.error(
                "保存系统回答失败",
                user_id=user_id,
                conversation_id=conversation_id,
                error=str(e)
            )
            raise DatabaseException(f"保存系统回答失败: {e}")


# 全局记忆管理器实例
memory_manager = MemoryManager()
