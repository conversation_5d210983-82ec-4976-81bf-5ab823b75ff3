# 🚀 AI智能报告生成系统

AI 驱动的文档处理与实时编译预览平台：Word/PDF → Markdown/图表 → Typst 向量，配合 AI 写作助手与前端预览。

## 📋 项目概述

本系统提供端到端的文档处理与报告生成能力：从上传、解析、结构化与图表生成，到 Typst 高质量排版与前端实时预览。最新引入基于 WebSocket 的 Typst 实时编译链路，让编辑器获得接近 IDE 的“所见即所得”体验。并提供AI助手进行修改。

### 🎯 核心功能
- 文档处理：Word/PDF 解析（结构/表格/图表/图片），图表元数据抽取与 D3 配置生成。
- AI 写作助手：上下文增强的流式写作与润色（SSE），无阻塞前端体验。
- Typst 集成：Markdown → Typst → 向量工件（`.artifact.sir.in`，typst-ts-cli），稳定预览；可扩展到 PDF 导出。
- 实时编译：WebSocket 驱动的实时编译反馈（连接确认/进度/完成/错误），健康检查与超时保护。
- 安全与配置：后端 JWT/Bearer；前端 Next.js 服务端 API 通过 HttpOnly Cookie 托管令牌；Redis 可选接入与 PING 自检。

## 🏗️ 架构一览

### 完整处理流程
```
Word/PDF文档上传 → 内容解析 → AI优化 → Web展示 → 实时编辑 → Typst编译
     ↓             ↓         ↓        ↓         ↓          ↓
   mammoth       图表提取  火山引擎API  D3.js渲染  流式AI助手  Typst生成
   docx/PDF      XML解析   智能优化   响应式布局   实时更新     专业排版
   Coze工作流    Rust引擎   上下文理解  模板渲染   Markdown    
```

### 技术架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend       │    │   AI Services   │
│                 │    │                  │    │                 │
│ • Next.js 15    │◄──►│ • FastAPI        │◄──►│ • 火山引擎API    │
│ • React 18      │    │ • SQLAlchemy     │    │ • OpenAI兼容    │
│ • TipTap Editor │    │ • Alembic        │    │ • 流式响应      │
│ • SSE Client    │    │ • SSE Streaming  │    │ • 上下文理解    │
│ • D3.js Charts  │    │ • CORS Support   │    │ • 智能优化      │
│ • Typst Viewer  │    │ • Rust Tools     │    │ • PDF解析       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              ↕
                    ┌─────────────────┐
                    │  Typst Engine   │
                    │                 │
                    │ • Rust核心      │
                    │ • Python绑定    │
                    │ • 模板系统      │
                    │ • 字体支持      │
                    │                 │
                    └─────────────────┘
```

## 🆕 最新更新（2025-09-03）

本次覆盖 2025-09-02 → 2025-09-03 的提交：

- refactor(templates): 采用 Typst 原生 import 的极简模板系统；移除 username 绑定，新增示例模板与配置
  - 新增/更新：`assets/templates/templates.json`、`assets/templates/simple/simple.typ`、`assets/templates/travel/travel.typ`
  - 服务层支持：`services/markdown_to_typst_service.py: apply_template_to_content()` 使用相对路径导入并拼接 `#show: template`
  - 新文档：`docs/SIMPLE_TEMPLATE_SYSTEM.md`
- feat(frontend): 集成模板选择与实时编译
  - 新组件：`web/src/components/templates/typst/TypstTemplateSelector.tsx`
  - 页面集成：右侧模板栏（TemplateBar），切换立即触发重编译 `compileReport()`（参见 `web/src/app/page.tsx`）
  - 新 Hook：`web/src/hooks/useCompilationService.ts`
- refactor(ai-editor): 阶段性重构与持久化修复，拆分/整合编辑器模块以提升可维护性
- docs: 前端组件最佳实践补充（见 `CLAUDE.md`）
- refactor(organization): 组件结构梳理与命名统一

提示：模板系统说明与扩展方式详见 `docs/SIMPLE_TEMPLATE_SYSTEM.md`。

## 🆕 先前更新（2025-08-27）

feat(typst-integration): Typst 实时编译与预览 + WebSocket 稳定性提升 + Redis 接入

- 新增完整编译链路：`/api/typst-compile/{health,compile-full,test-conversion}`（HTTP）+ `/ws/compile/{session}`（WS）。
- 引入 typst-ts-cli，输出向量工件 `.artifact.sir.in`，前端以 SVG/Canvas 稳定预览，禁用动画避免闪烁。
- WebSocket 增强：连接建立确认、服务预热、30s 超时保护、错误恢复与用户提示。
- 结构化日志与降噪：`core/logging.py` 默认减少 SQLAlchemy 噪音；严重错误具备上下文。
- Redis 可选接入：`database/redis_client.py` + `.env.example` 中 `REDIS_*`，带 PING 自检。
- 前端鉴权改为服务端托管 HttpOnly Cookies，降低敏感信息暴露风险。
## 🆕 先前更新 (2025-08-15)

### 📄 PDF智能解析支持 ✅
- ✅ **Coze工作流集成**: 基于Coze API的PDF智能解析
- ✅ **PDF上传接口**: 新增 `/upload-pdf` 端点支持PDF文件处理
- ✅ **智能格式转换**: PDF自动转换为结构化Markdown
- ✅ **文件管理**: PDF文件和解析结果统一存储在 `tmp/` 目录
- ✅ **测试工具**: 完整的PDF解析流程测试脚本
- ✅ **环境配置**: Coze API密钥和工作流ID配置支持

### 🛠️ 数据库结构优化 ✅
- ✅ **字段约束加强**: processed_documents表中tables和charts字段设为非空
- ✅ **默认值处理**: 空列表作为JSON字段默认值，确保数据一致性
- ✅ **迁移脚本**: 完整的Alembic数据库迁移支持
- ✅ **错误防护**: 防止AI服务不可用时的空引用错误

### 🔒 安全加固实施 - 全面验证通过 ✅
- ✅ **JWT认证系统**: 完整的Bearer令牌认证，支持刷新令牌旋转
- ✅ **基于角色的访问控制**: 用户角色管理，bcrypt密码加密
- ✅ **速率限制保护**: 内存滑动窗口算法 (登录10/60s, 刷新30/60s, AI生成60/60s)
- ✅ **安全头部设置**: OWASP标准安全头，请求追踪标识
- ✅ **文件上传安全**: 路径遍历防护，魔数签名验证
- ✅ **SSE流式认证**: 服务器推送事件的完整身份验证
- ✅ **前端集成**: 自动令牌注入，CORS配置，代理转发

### 🌐 Next.js服务层架构重构 ✅
- ✅ **统一服务层**: Next.js API路由作为中间件处理前后端通信
- ✅ **TypeScript类型安全**: 增强的Chart、Table和UI组件类型定义
- ✅ **ESLint规则优化**: 改进的类型安全和认证工具使用规则
- ✅ **API客户端重构**: 使用Next.js服务层的统一API客户端
- ✅ **认证流程优化**: 改进的认证处理和错误管理
- ✅ **代理路由**: 移除直接后端端口暴露，增强安全性
## 🆕 先前更新 (2025-08-07)
- ✅ **代码重构**: 简化了服务，移除了数据查询和Text2SQL功能，专注于核心文档处理和AI写作。
- ✅ **实时流式AI响应**: 完整的Server-Sent Events实现
- ✅ **AI写作助手界面**: 现代化的模态界面，支持长内容滚动
- ✅ **多种AI功能**: 文本改进、缩短、扩展、语法修正等
- ✅ **响应式设计**: 完善的移动端和桌面端适配
- ✅ **性能优化**: 毫秒级响应和流畅的用户体验

## 🛠️ 技术栈

### 后端
- Python 3.13、FastAPI、SQLAlchemy、Alembic、MySQL（PyMySQL）
- SSE（AI 写作流式）、WebSocket（Typst 实时编译）
- structlog + Rich 日志、可选 Redis 客户端

### 前端
- Next.js 15、React 18、TypeScript、TipTap/Novel
- TypstCoreViewer（向量预览）、D3.js
- 服务端 API 路由：鉴权与后端代理（HttpOnly Cookies）

### Typst 工具链
- typst-ts-cli（向量工件生成）、官方 Typst CLI（可选 PDF）
- 字体：Noto CJK 等，`setup_fonts.py` 一键安装/验证

## 🚀 快速开始（本地开发）

### 1) 前置要求
- Python 3.13+
- Node.js 24+（建议安装 pnpm）
- MySQL 8.0+
- Redis（可选）
- typst-ts-cli（确保 `typst-ts-cli --version` 可用）

### 2) 克隆与后端安装
```bash
git clone <repository-url>
cd ai-report-gen

# Python 依赖
uv install

# 环境变量
cp .env.example .env

# 数据库迁移
alembic upgrade head

# 启动后端
uv run python main.py  # 打开 http://localhost:8000/docs
```

### 3) Typst 前置准备（强烈建议）
```bash
# CJK 字体与模板（如需中文文档）
uv run python setup_fonts.py

# typst-ts-cli 安装（使用 Cargo 安装/NPM）
# - 参考官方文档安装： https://github.com/Myriad-Dreamin/typst.ts
# - 或使用包管理器（示例）：
#   cargo install --locked --git https://github.com/Myriad-Dreamin/typst.ts typst-ts-cli
安装完成后，可以用下面命令验证：
typst-ts-cli --version
#   pnpm install -g typst.ts-cli

# 验证命令可用
typst-ts-cli --help
```

### 4) 前端安装
```bash
cd web
pnpm install
echo "NEXT_PUBLIC_AI_BACKEND_URL=http://localhost:8000" > .env.local
pnpm dev  # http://localhost:3000
```

### 5) 基础验证
- API 文档: http://localhost:8000/docs
- Typst 健康检查: http://localhost:8000/api/typst-compile/health
- WebSocket 回归脚本: `uv run python scripts/test_websocket_fix.py`
- 模板应用测试: `uv run python scripts/test_template_application.py`

## 🔌 API 摘要

- WebSocket: `GET /ws/compile/{session_id}`
  - 事件：`connection_established` → `compilation_started`/`compilation_progress` → `compilation_complete` | `compilation_error`
  - 说明：连接确认带 `service_ready`；编译超时默认 30s；错误带 `retry_after` 提示。

- HTTP: `/api/typst-compile`
  - `GET /health`：线程池与服务就绪检查。
  - `POST /compile-full`：Markdown → Typst → Vector（需要鉴权 + 限流）。支持可选字段 `template_name` 用于应用模板。
  - `POST /test-conversion`：仅执行 Markdown → Typst 转换。

## 🔒 认证与安全

- 后端：JWT Bearer（OpenAPI 中 `Authorize` 可测试），`core/security.py` 统一验证。
- 前端：Next.js API 路由托管 HttpOnly Cookies（安全、可控），代理向后端注入 `Authorization`。
- 速率限制：关键接口具有限流依赖；日志采用结构化输出。

## 🧰 目录结构

```
api/                    # FastAPI 路由层
├── auth_routes.py      # JWT 认证与用户管理
├── ai_stream_routes.py # SSE 流式 AI 响应
├── word_format_routes.py # Word 文档处理
├── pdf_routes.py       # PDF 文档处理（Coze 工作流）
├── typst_compile_routes.py # Typst 编译服务（HTTP 接口）
├── websocket_routes.py # WebSocket 实时编译
└── report_routes.py    # 报告与图表配置

services/               # 核心业务逻辑
├── word_parser.py      # Word → Markdown 解析
├── pdf_parser.py       # PDF → Markdown（Coze API）
├── chart_xml_parser.py # 图表元数据提取
├── d3js_generator.py   # 图表配置生成
├── ai_chat_service.py  # LLM 调用与优化
├── markdown_to_typst_service.py # Markdown → Typst 转换
└── auth_service.py     # 认证与会话管理

core/                   # 核心基础设施
├── security.py         # JWT 验证与 RBAC
├── rate_limit.py       # 限流控制
├── logging.py          # 结构化日志
├── request_context.py  # 请求上下文
└── security_headers.py # OWASP 安全头

database/               # 数据层
├── connection.py       # SQLAlchemy 会话
├── redis_client.py     # Redis 客户端（可选）
└── memory.py          # 内存存储

models/                 # 数据模型
├── database.py         # SQLAlchemy 模型
└── schemas.py          # Pydantic 模式

web/                    # Next.js 前端
├── src/app/           # App Router 页面
├── src/components/    # UI 组件
│   ├── ai-editor/     # TipTap 编辑器与 AI 助手
│   ├── chart-renderer.tsx # D3.js 图表渲染
│   ├── TypstCoreViewer.tsx # Typst 向量预览
│   └── templates/     # 模板选择组件（Typst/Markdown）
├── src/hooks/         # React Hooks
│   ├── useRealtimeCompilation.ts # WebSocket 编译
│   ├── useCompilationService.ts  # 前端编译服务封装
│   └── use-ai.ts      # SSE AI 流式响应
├── src/lib/           # 工具库
│   ├── api-client.ts  # API 客户端
│   └── server-auth.ts # 服务端认证
└── src/app/api/       # Next.js API 路由（代理层）

assets/                 # 静态资源
├── templates/         # Typst 模板（templates.json + *.typ）
└── reports/           # 示例报告文件

fonts/                  # 字体文件（CJK 支持）
rust-tools/            # Rust 工具链
├── extract-tool/      # 文档提取工具（Python 绑定）
└── cmarker-typst/     # Markdown → Typst 转换器

scripts/               # 测试与工具脚本
├── test_websocket_fix.py # WebSocket 回归测试
├── test_ai_optimization.py # AI 优化测试
└── setup_fonts.py     # 字体安装脚本

alembic/               # 数据库迁移
├── versions/          # 迁移文件
└── env.py            # 迁移环境配置

tmp/                   # 临时文件存储
├── typst/            # Typst 编译缓存
├── word_uploads/     # Word 上传文件
└── pdf_uploads/      # PDF 上传文件
```

## 🐛 故障排查（FAQ）

- 找不到 `typst-ts-cli`：确保安装并在 PATH；执行 `typst-ts-cli --version` 验证。
- CJK 字体缺失或乱码：运行 `uv run python setup_fonts.py`，并确认 `TYPST_FONT_PATHS` 指向 `fonts/:/tmp/typst-fonts`。
- 编译超时：检查内容复杂度、CPU 占用与字体可用性；适当减少内容或重试。
- 401/鉴权失败：后端需 Bearer；前端通过 API 路由设置/转发 Cookie，无需本地存储令牌。

---

更多协作与贡献规范，请参考 `AGENTS.md`。 
