# Rust Tools

This directory contains Rust-based tools for the AI Report Generator.

## cmarker-typst/
Core library for converting CommonMark (Markdown) to Typst format.

## extract-tool/
Python extension that wraps the cmarker-typst library for use in the main application.

## Building

### For Rust library only:
```bash
cd rust-tools/extract-tool
cargo build --release
```

### For Python wheel (recommended):
```bash
cd rust-tools/extract-tool
maturin build --release
```

The Python wheel will be generated in `target/wheels/` directory.

### Install locally for development:
```bash
cd rust-tools/extract-tool
maturin develop --release
```

## Python Bindings

The `extract-tool/` directory contains Python bindings for the cmarker-typst library using PyO3.

### Prerequisites:
- Python ≥ 3.7
- maturin (`pip install maturin`)

### Usage:
```python
import cmarker_python

# Convert markdown file to Typst format
result = cmarker_python.convert_md_to_typ("input.md", "output.typ")
print(result)
```