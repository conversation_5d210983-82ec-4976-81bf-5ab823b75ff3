[package]
name = "cmarker-typst"
version = "0.0.0"
edition = "2024"
publish = false

[lib]
path = "lib.rs"

[dependencies]
bitflags = "2.4.1"
foldhash = { version = "0.1.5", default-features = false }
hashbrown = "0.15.2"
itoa = "1.0.9"
memchr = { version = "2.7.4", default-features = false }
# https://github.com/pulldown-cmark/pulldown-cmark/pull/1026
pulldown-cmark.git = "https://github.com/SabrinaJewson/pulldown-cmark.rs"
pulldown-cmark.rev = "4203ce5bd8ef79f67357717d2b728579f3dec294"
pulldown-cmark.default-features = false
pulldown-cmark.features = ["hashbrown"]
phf = { version = "0.11.3", default-features = false }
ucd-trie = { version = "0.1.7", default-features = false }

[dev-dependencies]
typst-syntax = "0.13.1"

[build-dependencies]
icu_properties = { version = "2.0.1", features = ["compiled_data"] }
phf_codegen = "0.11.3"
serde = { version = "1.0.218", features = ["derive"] }
serde_json = "1.0.140"
ucd-trie = { version = "0.1.7", features = ["std"] }
