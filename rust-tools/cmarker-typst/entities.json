{"&AElig": {"codepoints": [198], "characters": "<PERSON>"}, "&AElig;": {"codepoints": [198], "characters": "<PERSON>"}, "&AMP": {"codepoints": [38], "characters": "&"}, "&AMP;": {"codepoints": [38], "characters": "&"}, "&Aacute": {"codepoints": [193], "characters": "Á"}, "&Aacute;": {"codepoints": [193], "characters": "Á"}, "&Abreve;": {"codepoints": [258], "characters": "Ă"}, "&Acirc": {"codepoints": [194], "characters": "Â"}, "&Acirc;": {"codepoints": [194], "characters": "Â"}, "&Acy;": {"codepoints": [1040], "characters": "А"}, "&Afr;": {"codepoints": [120068], "characters": "𝔄"}, "&Agrave": {"codepoints": [192], "characters": "À"}, "&Agrave;": {"codepoints": [192], "characters": "À"}, "&Alpha;": {"codepoints": [913], "characters": "Α"}, "&Amacr;": {"codepoints": [256], "characters": "Ā"}, "&And;": {"codepoints": [10835], "characters": "⩓"}, "&Aogon;": {"codepoints": [260], "characters": "Ą"}, "&Aopf;": {"codepoints": [120120], "characters": "𝔸"}, "&ApplyFunction;": {"codepoints": [8289], "characters": "⁡"}, "&Aring": {"codepoints": [197], "characters": "Å"}, "&Aring;": {"codepoints": [197], "characters": "Å"}, "&Ascr;": {"codepoints": [119964], "characters": "𝒜"}, "&Assign;": {"codepoints": [8788], "characters": "≔"}, "&Atilde": {"codepoints": [195], "characters": "Ã"}, "&Atilde;": {"codepoints": [195], "characters": "Ã"}, "&Auml": {"codepoints": [196], "characters": "Ä"}, "&Auml;": {"codepoints": [196], "characters": "Ä"}, "&Backslash;": {"codepoints": [8726], "characters": "∖"}, "&Barv;": {"codepoints": [10983], "characters": "⫧"}, "&Barwed;": {"codepoints": [8966], "characters": "⌆"}, "&Bcy;": {"codepoints": [1041], "characters": "Б"}, "&Because;": {"codepoints": [8757], "characters": "∵"}, "&Bernoullis;": {"codepoints": [8492], "characters": "ℬ"}, "&Beta;": {"codepoints": [914], "characters": "Β"}, "&Bfr;": {"codepoints": [120069], "characters": "𝔅"}, "&Bopf;": {"codepoints": [120121], "characters": "𝔹"}, "&Breve;": {"codepoints": [728], "characters": "˘"}, "&Bscr;": {"codepoints": [8492], "characters": "ℬ"}, "&Bumpeq;": {"codepoints": [8782], "characters": "≎"}, "&CHcy;": {"codepoints": [1063], "characters": "Ч"}, "&COPY": {"codepoints": [169], "characters": "©"}, "&COPY;": {"codepoints": [169], "characters": "©"}, "&Cacute;": {"codepoints": [262], "characters": "Ć"}, "&Cap;": {"codepoints": [8914], "characters": "⋒"}, "&CapitalDifferentialD;": {"codepoints": [8517], "characters": "ⅅ"}, "&Cayleys;": {"codepoints": [8493], "characters": "ℭ"}, "&Ccaron;": {"codepoints": [268], "characters": "Č"}, "&Ccedil": {"codepoints": [199], "characters": "Ç"}, "&Ccedil;": {"codepoints": [199], "characters": "Ç"}, "&Ccirc;": {"codepoints": [264], "characters": "Ĉ"}, "&Cconint;": {"codepoints": [8752], "characters": "∰"}, "&Cdot;": {"codepoints": [266], "characters": "Ċ"}, "&Cedilla;": {"codepoints": [184], "characters": "¸"}, "&CenterDot;": {"codepoints": [183], "characters": "·"}, "&Cfr;": {"codepoints": [8493], "characters": "ℭ"}, "&Chi;": {"codepoints": [935], "characters": "Χ"}, "&CircleDot;": {"codepoints": [8857], "characters": "⊙"}, "&CircleMinus;": {"codepoints": [8854], "characters": "⊖"}, "&CirclePlus;": {"codepoints": [8853], "characters": "⊕"}, "&CircleTimes;": {"codepoints": [8855], "characters": "⊗"}, "&ClockwiseContourIntegral;": {"codepoints": [8754], "characters": "∲"}, "&CloseCurlyDoubleQuote;": {"codepoints": [8221], "characters": "”"}, "&CloseCurlyQuote;": {"codepoints": [8217], "characters": "’"}, "&Colon;": {"codepoints": [8759], "characters": "∷"}, "&Colone;": {"codepoints": [10868], "characters": "⩴"}, "&Congruent;": {"codepoints": [8801], "characters": "≡"}, "&Conint;": {"codepoints": [8751], "characters": "∯"}, "&ContourIntegral;": {"codepoints": [8750], "characters": "∮"}, "&Copf;": {"codepoints": [8450], "characters": "ℂ"}, "&Coproduct;": {"codepoints": [8720], "characters": "∐"}, "&CounterClockwiseContourIntegral;": {"codepoints": [8755], "characters": "∳"}, "&Cross;": {"codepoints": [10799], "characters": "⨯"}, "&Cscr;": {"codepoints": [119966], "characters": "𝒞"}, "&Cup;": {"codepoints": [8915], "characters": "⋓"}, "&CupCap;": {"codepoints": [8781], "characters": "≍"}, "&DD;": {"codepoints": [8517], "characters": "ⅅ"}, "&DDotrahd;": {"codepoints": [10513], "characters": "⤑"}, "&DJcy;": {"codepoints": [1026], "characters": "Ђ"}, "&DScy;": {"codepoints": [1029], "characters": "Ѕ"}, "&DZcy;": {"codepoints": [1039], "characters": "Џ"}, "&Dagger;": {"codepoints": [8225], "characters": "‡"}, "&Darr;": {"codepoints": [8609], "characters": "↡"}, "&Dashv;": {"codepoints": [10980], "characters": "⫤"}, "&Dcaron;": {"codepoints": [270], "characters": "Ď"}, "&Dcy;": {"codepoints": [1044], "characters": "Д"}, "&Del;": {"codepoints": [8711], "characters": "∇"}, "&Delta;": {"codepoints": [916], "characters": "Δ"}, "&Dfr;": {"codepoints": [120071], "characters": "𝔇"}, "&DiacriticalAcute;": {"codepoints": [180], "characters": "´"}, "&DiacriticalDot;": {"codepoints": [729], "characters": "˙"}, "&DiacriticalDoubleAcute;": {"codepoints": [733], "characters": "˝"}, "&DiacriticalGrave;": {"codepoints": [96], "characters": "`"}, "&DiacriticalTilde;": {"codepoints": [732], "characters": "˜"}, "&Diamond;": {"codepoints": [8900], "characters": "⋄"}, "&DifferentialD;": {"codepoints": [8518], "characters": "ⅆ"}, "&Dopf;": {"codepoints": [120123], "characters": "𝔻"}, "&Dot;": {"codepoints": [168], "characters": "¨"}, "&DotDot;": {"codepoints": [8412], "characters": "⃜"}, "&DotEqual;": {"codepoints": [8784], "characters": "≐"}, "&DoubleContourIntegral;": {"codepoints": [8751], "characters": "∯"}, "&DoubleDot;": {"codepoints": [168], "characters": "¨"}, "&DoubleDownArrow;": {"codepoints": [8659], "characters": "⇓"}, "&DoubleLeftArrow;": {"codepoints": [8656], "characters": "⇐"}, "&DoubleLeftRightArrow;": {"codepoints": [8660], "characters": "⇔"}, "&DoubleLeftTee;": {"codepoints": [10980], "characters": "⫤"}, "&DoubleLongLeftArrow;": {"codepoints": [10232], "characters": "⟸"}, "&DoubleLongLeftRightArrow;": {"codepoints": [10234], "characters": "⟺"}, "&DoubleLongRightArrow;": {"codepoints": [10233], "characters": "⟹"}, "&DoubleRightArrow;": {"codepoints": [8658], "characters": "⇒"}, "&DoubleRightTee;": {"codepoints": [8872], "characters": "⊨"}, "&DoubleUpArrow;": {"codepoints": [8657], "characters": "⇑"}, "&DoubleUpDownArrow;": {"codepoints": [8661], "characters": "⇕"}, "&DoubleVerticalBar;": {"codepoints": [8741], "characters": "∥"}, "&DownArrow;": {"codepoints": [8595], "characters": "↓"}, "&DownArrowBar;": {"codepoints": [10515], "characters": "⤓"}, "&DownArrowUpArrow;": {"codepoints": [8693], "characters": "⇵"}, "&DownBreve;": {"codepoints": [785], "characters": "̑"}, "&DownLeftRightVector;": {"codepoints": [10576], "characters": "⥐"}, "&DownLeftTeeVector;": {"codepoints": [10590], "characters": "⥞"}, "&DownLeftVector;": {"codepoints": [8637], "characters": "↽"}, "&DownLeftVectorBar;": {"codepoints": [10582], "characters": "⥖"}, "&DownRightTeeVector;": {"codepoints": [10591], "characters": "⥟"}, "&DownRightVector;": {"codepoints": [8641], "characters": "⇁"}, "&DownRightVectorBar;": {"codepoints": [10583], "characters": "⥗"}, "&DownTee;": {"codepoints": [8868], "characters": "⊤"}, "&DownTeeArrow;": {"codepoints": [8615], "characters": "↧"}, "&Downarrow;": {"codepoints": [8659], "characters": "⇓"}, "&Dscr;": {"codepoints": [119967], "characters": "𝒟"}, "&Dstrok;": {"codepoints": [272], "characters": "Đ"}, "&ENG;": {"codepoints": [330], "characters": "Ŋ"}, "&ETH": {"codepoints": [208], "characters": "Ð"}, "&ETH;": {"codepoints": [208], "characters": "Ð"}, "&Eacute": {"codepoints": [201], "characters": "É"}, "&Eacute;": {"codepoints": [201], "characters": "É"}, "&Ecaron;": {"codepoints": [282], "characters": "Ě"}, "&Ecirc": {"codepoints": [202], "characters": "Ê"}, "&Ecirc;": {"codepoints": [202], "characters": "Ê"}, "&Ecy;": {"codepoints": [1069], "characters": "Э"}, "&Edot;": {"codepoints": [278], "characters": "Ė"}, "&Efr;": {"codepoints": [120072], "characters": "𝔈"}, "&Egrave": {"codepoints": [200], "characters": "È"}, "&Egrave;": {"codepoints": [200], "characters": "È"}, "&Element;": {"codepoints": [8712], "characters": "∈"}, "&Emacr;": {"codepoints": [274], "characters": "Ē"}, "&EmptySmallSquare;": {"codepoints": [9723], "characters": "◻"}, "&EmptyVerySmallSquare;": {"codepoints": [9643], "characters": "▫"}, "&Eogon;": {"codepoints": [280], "characters": "Ę"}, "&Eopf;": {"codepoints": [120124], "characters": "𝔼"}, "&Epsilon;": {"codepoints": [917], "characters": "Ε"}, "&Equal;": {"codepoints": [10869], "characters": "⩵"}, "&EqualTilde;": {"codepoints": [8770], "characters": "≂"}, "&Equilibrium;": {"codepoints": [8652], "characters": "⇌"}, "&Escr;": {"codepoints": [8496], "characters": "ℰ"}, "&Esim;": {"codepoints": [10867], "characters": "⩳"}, "&Eta;": {"codepoints": [919], "characters": "Η"}, "&Euml": {"codepoints": [203], "characters": "Ë"}, "&Euml;": {"codepoints": [203], "characters": "Ë"}, "&Exists;": {"codepoints": [8707], "characters": "∃"}, "&ExponentialE;": {"codepoints": [8519], "characters": "ⅇ"}, "&Fcy;": {"codepoints": [1060], "characters": "Ф"}, "&Ffr;": {"codepoints": [120073], "characters": "𝔉"}, "&FilledSmallSquare;": {"codepoints": [9724], "characters": "◼"}, "&FilledVerySmallSquare;": {"codepoints": [9642], "characters": "▪"}, "&Fopf;": {"codepoints": [120125], "characters": "𝔽"}, "&ForAll;": {"codepoints": [8704], "characters": "∀"}, "&Fouriertrf;": {"codepoints": [8497], "characters": "ℱ"}, "&Fscr;": {"codepoints": [8497], "characters": "ℱ"}, "&GJcy;": {"codepoints": [1027], "characters": "Ѓ"}, "&GT": {"codepoints": [62], "characters": ">"}, "&GT;": {"codepoints": [62], "characters": ">"}, "&Gamma;": {"codepoints": [915], "characters": "Γ"}, "&Gammad;": {"codepoints": [988], "characters": "Ϝ"}, "&Gbreve;": {"codepoints": [286], "characters": "Ğ"}, "&Gcedil;": {"codepoints": [290], "characters": "Ģ"}, "&Gcirc;": {"codepoints": [284], "characters": "Ĝ"}, "&Gcy;": {"codepoints": [1043], "characters": "Г"}, "&Gdot;": {"codepoints": [288], "characters": "Ġ"}, "&Gfr;": {"codepoints": [120074], "characters": "𝔊"}, "&Gg;": {"codepoints": [8921], "characters": "⋙"}, "&Gopf;": {"codepoints": [120126], "characters": "𝔾"}, "&GreaterEqual;": {"codepoints": [8805], "characters": "≥"}, "&GreaterEqualLess;": {"codepoints": [8923], "characters": "⋛"}, "&GreaterFullEqual;": {"codepoints": [8807], "characters": "≧"}, "&GreaterGreater;": {"codepoints": [10914], "characters": "⪢"}, "&GreaterLess;": {"codepoints": [8823], "characters": "≷"}, "&GreaterSlantEqual;": {"codepoints": [10878], "characters": "⩾"}, "&GreaterTilde;": {"codepoints": [8819], "characters": "≳"}, "&Gscr;": {"codepoints": [119970], "characters": "𝒢"}, "&Gt;": {"codepoints": [8811], "characters": "≫"}, "&HARDcy;": {"codepoints": [1066], "characters": "Ъ"}, "&Hacek;": {"codepoints": [711], "characters": "ˇ"}, "&Hat;": {"codepoints": [94], "characters": "^"}, "&Hcirc;": {"codepoints": [292], "characters": "Ĥ"}, "&Hfr;": {"codepoints": [8460], "characters": "ℌ"}, "&HilbertSpace;": {"codepoints": [8459], "characters": "ℋ"}, "&Hopf;": {"codepoints": [8461], "characters": "ℍ"}, "&HorizontalLine;": {"codepoints": [9472], "characters": "─"}, "&Hscr;": {"codepoints": [8459], "characters": "ℋ"}, "&Hstrok;": {"codepoints": [294], "characters": "Ħ"}, "&HumpDownHump;": {"codepoints": [8782], "characters": "≎"}, "&HumpEqual;": {"codepoints": [8783], "characters": "≏"}, "&IEcy;": {"codepoints": [1045], "characters": "Е"}, "&IJlig;": {"codepoints": [306], "characters": "Ĳ"}, "&IOcy;": {"codepoints": [1025], "characters": "Ё"}, "&Iacute": {"codepoints": [205], "characters": "Í"}, "&Iacute;": {"codepoints": [205], "characters": "Í"}, "&Icirc": {"codepoints": [206], "characters": "Î"}, "&Icirc;": {"codepoints": [206], "characters": "Î"}, "&Icy;": {"codepoints": [1048], "characters": "И"}, "&Idot;": {"codepoints": [304], "characters": "İ"}, "&Ifr;": {"codepoints": [8465], "characters": "ℑ"}, "&Igrave": {"codepoints": [204], "characters": "Ì"}, "&Igrave;": {"codepoints": [204], "characters": "Ì"}, "&Im;": {"codepoints": [8465], "characters": "ℑ"}, "&Imacr;": {"codepoints": [298], "characters": "Ī"}, "&ImaginaryI;": {"codepoints": [8520], "characters": "ⅈ"}, "&Implies;": {"codepoints": [8658], "characters": "⇒"}, "&Int;": {"codepoints": [8748], "characters": "∬"}, "&Integral;": {"codepoints": [8747], "characters": "∫"}, "&Intersection;": {"codepoints": [8898], "characters": "⋂"}, "&InvisibleComma;": {"codepoints": [8291], "characters": "⁣"}, "&InvisibleTimes;": {"codepoints": [8290], "characters": "⁢"}, "&Iogon;": {"codepoints": [302], "characters": "Į"}, "&Iopf;": {"codepoints": [120128], "characters": "𝕀"}, "&Iota;": {"codepoints": [921], "characters": "Ι"}, "&Iscr;": {"codepoints": [8464], "characters": "ℐ"}, "&Itilde;": {"codepoints": [296], "characters": "Ĩ"}, "&Iukcy;": {"codepoints": [1030], "characters": "І"}, "&Iuml": {"codepoints": [207], "characters": "Ï"}, "&Iuml;": {"codepoints": [207], "characters": "Ï"}, "&Jcirc;": {"codepoints": [308], "characters": "Ĵ"}, "&Jcy;": {"codepoints": [1049], "characters": "Й"}, "&Jfr;": {"codepoints": [120077], "characters": "𝔍"}, "&Jopf;": {"codepoints": [120129], "characters": "𝕁"}, "&Jscr;": {"codepoints": [119973], "characters": "𝒥"}, "&Jsercy;": {"codepoints": [1032], "characters": "Ј"}, "&Jukcy;": {"codepoints": [1028], "characters": "Є"}, "&KHcy;": {"codepoints": [1061], "characters": "Х"}, "&KJcy;": {"codepoints": [1036], "characters": "Ќ"}, "&Kappa;": {"codepoints": [922], "characters": "Κ"}, "&Kcedil;": {"codepoints": [310], "characters": "Ķ"}, "&Kcy;": {"codepoints": [1050], "characters": "К"}, "&Kfr;": {"codepoints": [120078], "characters": "𝔎"}, "&Kopf;": {"codepoints": [120130], "characters": "𝕂"}, "&Kscr;": {"codepoints": [119974], "characters": "𝒦"}, "&LJcy;": {"codepoints": [1033], "characters": "Љ"}, "&LT": {"codepoints": [60], "characters": "<"}, "&LT;": {"codepoints": [60], "characters": "<"}, "&Lacute;": {"codepoints": [313], "characters": "Ĺ"}, "&Lambda;": {"codepoints": [923], "characters": "Λ"}, "&Lang;": {"codepoints": [10218], "characters": "⟪"}, "&Laplacetrf;": {"codepoints": [8466], "characters": "ℒ"}, "&Larr;": {"codepoints": [8606], "characters": "↞"}, "&Lcaron;": {"codepoints": [317], "characters": "Ľ"}, "&Lcedil;": {"codepoints": [315], "characters": "Ļ"}, "&Lcy;": {"codepoints": [1051], "characters": "Л"}, "&LeftAngleBracket;": {"codepoints": [10216], "characters": "⟨"}, "&LeftArrow;": {"codepoints": [8592], "characters": "←"}, "&LeftArrowBar;": {"codepoints": [8676], "characters": "⇤"}, "&LeftArrowRightArrow;": {"codepoints": [8646], "characters": "⇆"}, "&LeftCeiling;": {"codepoints": [8968], "characters": "⌈"}, "&LeftDoubleBracket;": {"codepoints": [10214], "characters": "⟦"}, "&LeftDownTeeVector;": {"codepoints": [10593], "characters": "⥡"}, "&LeftDownVector;": {"codepoints": [8643], "characters": "⇃"}, "&LeftDownVectorBar;": {"codepoints": [10585], "characters": "⥙"}, "&LeftFloor;": {"codepoints": [8970], "characters": "⌊"}, "&LeftRightArrow;": {"codepoints": [8596], "characters": "↔"}, "&LeftRightVector;": {"codepoints": [10574], "characters": "⥎"}, "&LeftTee;": {"codepoints": [8867], "characters": "⊣"}, "&LeftTeeArrow;": {"codepoints": [8612], "characters": "↤"}, "&LeftTeeVector;": {"codepoints": [10586], "characters": "⥚"}, "&LeftTriangle;": {"codepoints": [8882], "characters": "⊲"}, "&LeftTriangleBar;": {"codepoints": [10703], "characters": "⧏"}, "&LeftTriangleEqual;": {"codepoints": [8884], "characters": "⊴"}, "&LeftUpDownVector;": {"codepoints": [10577], "characters": "⥑"}, "&LeftUpTeeVector;": {"codepoints": [10592], "characters": "⥠"}, "&LeftUpVector;": {"codepoints": [8639], "characters": "↿"}, "&LeftUpVectorBar;": {"codepoints": [10584], "characters": "⥘"}, "&LeftVector;": {"codepoints": [8636], "characters": "↼"}, "&LeftVectorBar;": {"codepoints": [10578], "characters": "⥒"}, "&Leftarrow;": {"codepoints": [8656], "characters": "⇐"}, "&Leftrightarrow;": {"codepoints": [8660], "characters": "⇔"}, "&LessEqualGreater;": {"codepoints": [8922], "characters": "⋚"}, "&LessFullEqual;": {"codepoints": [8806], "characters": "≦"}, "&LessGreater;": {"codepoints": [8822], "characters": "≶"}, "&LessLess;": {"codepoints": [10913], "characters": "⪡"}, "&LessSlantEqual;": {"codepoints": [10877], "characters": "⩽"}, "&LessTilde;": {"codepoints": [8818], "characters": "≲"}, "&Lfr;": {"codepoints": [120079], "characters": "𝔏"}, "&Ll;": {"codepoints": [8920], "characters": "⋘"}, "&Lleftarrow;": {"codepoints": [8666], "characters": "⇚"}, "&Lmidot;": {"codepoints": [319], "characters": "Ŀ"}, "&LongLeftArrow;": {"codepoints": [10229], "characters": "⟵"}, "&LongLeftRightArrow;": {"codepoints": [10231], "characters": "⟷"}, "&LongRightArrow;": {"codepoints": [10230], "characters": "⟶"}, "&Longleftarrow;": {"codepoints": [10232], "characters": "⟸"}, "&Longleftrightarrow;": {"codepoints": [10234], "characters": "⟺"}, "&Longrightarrow;": {"codepoints": [10233], "characters": "⟹"}, "&Lopf;": {"codepoints": [120131], "characters": "𝕃"}, "&LowerLeftArrow;": {"codepoints": [8601], "characters": "↙"}, "&LowerRightArrow;": {"codepoints": [8600], "characters": "↘"}, "&Lscr;": {"codepoints": [8466], "characters": "ℒ"}, "&Lsh;": {"codepoints": [8624], "characters": "↰"}, "&Lstrok;": {"codepoints": [321], "characters": "Ł"}, "&Lt;": {"codepoints": [8810], "characters": "≪"}, "&Map;": {"codepoints": [10501], "characters": "⤅"}, "&Mcy;": {"codepoints": [1052], "characters": "М"}, "&MediumSpace;": {"codepoints": [8287], "characters": " "}, "&Mellintrf;": {"codepoints": [8499], "characters": "ℳ"}, "&Mfr;": {"codepoints": [120080], "characters": "𝔐"}, "&MinusPlus;": {"codepoints": [8723], "characters": "∓"}, "&Mopf;": {"codepoints": [120132], "characters": "𝕄"}, "&Mscr;": {"codepoints": [8499], "characters": "ℳ"}, "&Mu;": {"codepoints": [924], "characters": "Μ"}, "&NJcy;": {"codepoints": [1034], "characters": "Њ"}, "&Nacute;": {"codepoints": [323], "characters": "Ń"}, "&Ncaron;": {"codepoints": [327], "characters": "Ň"}, "&Ncedil;": {"codepoints": [325], "characters": "Ņ"}, "&Ncy;": {"codepoints": [1053], "characters": "Н"}, "&NegativeMediumSpace;": {"codepoints": [8203], "characters": "​"}, "&NegativeThickSpace;": {"codepoints": [8203], "characters": "​"}, "&NegativeThinSpace;": {"codepoints": [8203], "characters": "​"}, "&NegativeVeryThinSpace;": {"codepoints": [8203], "characters": "​"}, "&NestedGreaterGreater;": {"codepoints": [8811], "characters": "≫"}, "&NestedLessLess;": {"codepoints": [8810], "characters": "≪"}, "&NewLine;": {"codepoints": [10], "characters": "\n"}, "&Nfr;": {"codepoints": [120081], "characters": "𝔑"}, "&NoBreak;": {"codepoints": [8288], "characters": "⁠"}, "&NonBreakingSpace;": {"codepoints": [160], "characters": " "}, "&Nopf;": {"codepoints": [8469], "characters": "ℕ"}, "&Not;": {"codepoints": [10988], "characters": "⫬"}, "&NotCongruent;": {"codepoints": [8802], "characters": "≢"}, "&NotCupCap;": {"codepoints": [8813], "characters": "≭"}, "&NotDoubleVerticalBar;": {"codepoints": [8742], "characters": "∦"}, "&NotElement;": {"codepoints": [8713], "characters": "∉"}, "&NotEqual;": {"codepoints": [8800], "characters": "≠"}, "&NotEqualTilde;": {"codepoints": [8770, 824], "characters": "≂̸"}, "&NotExists;": {"codepoints": [8708], "characters": "∄"}, "&NotGreater;": {"codepoints": [8815], "characters": "≯"}, "&NotGreaterEqual;": {"codepoints": [8817], "characters": "≱"}, "&NotGreaterFullEqual;": {"codepoints": [8807, 824], "characters": "≧̸"}, "&NotGreaterGreater;": {"codepoints": [8811, 824], "characters": "≫̸"}, "&NotGreaterLess;": {"codepoints": [8825], "characters": "≹"}, "&NotGreaterSlantEqual;": {"codepoints": [10878, 824], "characters": "⩾̸"}, "&NotGreaterTilde;": {"codepoints": [8821], "characters": "≵"}, "&NotHumpDownHump;": {"codepoints": [8782, 824], "characters": "≎̸"}, "&NotHumpEqual;": {"codepoints": [8783, 824], "characters": "≏̸"}, "&NotLeftTriangle;": {"codepoints": [8938], "characters": "⋪"}, "&NotLeftTriangleBar;": {"codepoints": [10703, 824], "characters": "⧏̸"}, "&NotLeftTriangleEqual;": {"codepoints": [8940], "characters": "⋬"}, "&NotLess;": {"codepoints": [8814], "characters": "≮"}, "&NotLessEqual;": {"codepoints": [8816], "characters": "≰"}, "&NotLessGreater;": {"codepoints": [8824], "characters": "≸"}, "&NotLessLess;": {"codepoints": [8810, 824], "characters": "≪̸"}, "&NotLessSlantEqual;": {"codepoints": [10877, 824], "characters": "⩽̸"}, "&NotLessTilde;": {"codepoints": [8820], "characters": "≴"}, "&NotNestedGreaterGreater;": {"codepoints": [10914, 824], "characters": "⪢̸"}, "&NotNestedLessLess;": {"codepoints": [10913, 824], "characters": "⪡̸"}, "&NotPrecedes;": {"codepoints": [8832], "characters": "⊀"}, "&NotPrecedesEqual;": {"codepoints": [10927, 824], "characters": "⪯̸"}, "&NotPrecedesSlantEqual;": {"codepoints": [8928], "characters": "⋠"}, "&NotReverseElement;": {"codepoints": [8716], "characters": "∌"}, "&NotRightTriangle;": {"codepoints": [8939], "characters": "⋫"}, "&NotRightTriangleBar;": {"codepoints": [10704, 824], "characters": "⧐̸"}, "&NotRightTriangleEqual;": {"codepoints": [8941], "characters": "⋭"}, "&NotSquareSubset;": {"codepoints": [8847, 824], "characters": "⊏̸"}, "&NotSquareSubsetEqual;": {"codepoints": [8930], "characters": "⋢"}, "&NotSquareSuperset;": {"codepoints": [8848, 824], "characters": "⊐̸"}, "&NotSquareSupersetEqual;": {"codepoints": [8931], "characters": "⋣"}, "&NotSubset;": {"codepoints": [8834, 8402], "characters": "⊂⃒"}, "&NotSubsetEqual;": {"codepoints": [8840], "characters": "⊈"}, "&NotSucceeds;": {"codepoints": [8833], "characters": "⊁"}, "&NotSucceedsEqual;": {"codepoints": [10928, 824], "characters": "⪰̸"}, "&NotSucceedsSlantEqual;": {"codepoints": [8929], "characters": "⋡"}, "&NotSucceedsTilde;": {"codepoints": [8831, 824], "characters": "≿̸"}, "&NotSuperset;": {"codepoints": [8835, 8402], "characters": "⊃⃒"}, "&NotSupersetEqual;": {"codepoints": [8841], "characters": "⊉"}, "&NotTilde;": {"codepoints": [8769], "characters": "≁"}, "&NotTildeEqual;": {"codepoints": [8772], "characters": "≄"}, "&NotTildeFullEqual;": {"codepoints": [8775], "characters": "≇"}, "&NotTildeTilde;": {"codepoints": [8777], "characters": "≉"}, "&NotVerticalBar;": {"codepoints": [8740], "characters": "∤"}, "&Nscr;": {"codepoints": [119977], "characters": "𝒩"}, "&Ntilde": {"codepoints": [209], "characters": "Ñ"}, "&Ntilde;": {"codepoints": [209], "characters": "Ñ"}, "&Nu;": {"codepoints": [925], "characters": "Ν"}, "&OElig;": {"codepoints": [338], "characters": "Œ"}, "&Oacute": {"codepoints": [211], "characters": "<PERSON>"}, "&Oacute;": {"codepoints": [211], "characters": "<PERSON>"}, "&Ocirc": {"codepoints": [212], "characters": "Ô"}, "&Ocirc;": {"codepoints": [212], "characters": "Ô"}, "&Ocy;": {"codepoints": [1054], "characters": "О"}, "&Odblac;": {"codepoints": [336], "characters": "Ő"}, "&Ofr;": {"codepoints": [120082], "characters": "𝔒"}, "&Ograve": {"codepoints": [210], "characters": "Ò"}, "&Ograve;": {"codepoints": [210], "characters": "Ò"}, "&Omacr;": {"codepoints": [332], "characters": "Ō"}, "&Omega;": {"codepoints": [937], "characters": "Ω"}, "&Omicron;": {"codepoints": [927], "characters": "Ο"}, "&Oopf;": {"codepoints": [120134], "characters": "𝕆"}, "&OpenCurlyDoubleQuote;": {"codepoints": [8220], "characters": "“"}, "&OpenCurlyQuote;": {"codepoints": [8216], "characters": "‘"}, "&Or;": {"codepoints": [10836], "characters": "⩔"}, "&Oscr;": {"codepoints": [119978], "characters": "𝒪"}, "&Oslash": {"codepoints": [216], "characters": "Ø"}, "&Oslash;": {"codepoints": [216], "characters": "Ø"}, "&Otilde": {"codepoints": [213], "characters": "Õ"}, "&Otilde;": {"codepoints": [213], "characters": "Õ"}, "&Otimes;": {"codepoints": [10807], "characters": "⨷"}, "&Ouml": {"codepoints": [214], "characters": "Ö"}, "&Ouml;": {"codepoints": [214], "characters": "Ö"}, "&OverBar;": {"codepoints": [8254], "characters": "‾"}, "&OverBrace;": {"codepoints": [9182], "characters": "⏞"}, "&OverBracket;": {"codepoints": [9140], "characters": "⎴"}, "&OverParenthesis;": {"codepoints": [9180], "characters": "⏜"}, "&PartialD;": {"codepoints": [8706], "characters": "∂"}, "&Pcy;": {"codepoints": [1055], "characters": "П"}, "&Pfr;": {"codepoints": [120083], "characters": "𝔓"}, "&Phi;": {"codepoints": [934], "characters": "Φ"}, "&Pi;": {"codepoints": [928], "characters": "Π"}, "&PlusMinus;": {"codepoints": [177], "characters": "±"}, "&Poincareplane;": {"codepoints": [8460], "characters": "ℌ"}, "&Popf;": {"codepoints": [8473], "characters": "ℙ"}, "&Pr;": {"codepoints": [10939], "characters": "⪻"}, "&Precedes;": {"codepoints": [8826], "characters": "≺"}, "&PrecedesEqual;": {"codepoints": [10927], "characters": "⪯"}, "&PrecedesSlantEqual;": {"codepoints": [8828], "characters": "≼"}, "&PrecedesTilde;": {"codepoints": [8830], "characters": "≾"}, "&Prime;": {"codepoints": [8243], "characters": "″"}, "&Product;": {"codepoints": [8719], "characters": "∏"}, "&Proportion;": {"codepoints": [8759], "characters": "∷"}, "&Proportional;": {"codepoints": [8733], "characters": "∝"}, "&Pscr;": {"codepoints": [119979], "characters": "𝒫"}, "&Psi;": {"codepoints": [936], "characters": "Ψ"}, "&QUOT": {"codepoints": [34], "characters": "\""}, "&QUOT;": {"codepoints": [34], "characters": "\""}, "&Qfr;": {"codepoints": [120084], "characters": "𝔔"}, "&Qopf;": {"codepoints": [8474], "characters": "ℚ"}, "&Qscr;": {"codepoints": [119980], "characters": "𝒬"}, "&RBarr;": {"codepoints": [10512], "characters": "⤐"}, "&REG": {"codepoints": [174], "characters": "®"}, "&REG;": {"codepoints": [174], "characters": "®"}, "&Racute;": {"codepoints": [340], "characters": "Ŕ"}, "&Rang;": {"codepoints": [10219], "characters": "⟫"}, "&Rarr;": {"codepoints": [8608], "characters": "↠"}, "&Rarrtl;": {"codepoints": [10518], "characters": "⤖"}, "&Rcaron;": {"codepoints": [344], "characters": "Ř"}, "&Rcedil;": {"codepoints": [342], "characters": "Ŗ"}, "&Rcy;": {"codepoints": [1056], "characters": "Р"}, "&Re;": {"codepoints": [8476], "characters": "ℜ"}, "&ReverseElement;": {"codepoints": [8715], "characters": "∋"}, "&ReverseEquilibrium;": {"codepoints": [8651], "characters": "⇋"}, "&ReverseUpEquilibrium;": {"codepoints": [10607], "characters": "⥯"}, "&Rfr;": {"codepoints": [8476], "characters": "ℜ"}, "&Rho;": {"codepoints": [929], "characters": "Ρ"}, "&RightAngleBracket;": {"codepoints": [10217], "characters": "⟩"}, "&RightArrow;": {"codepoints": [8594], "characters": "→"}, "&RightArrowBar;": {"codepoints": [8677], "characters": "⇥"}, "&RightArrowLeftArrow;": {"codepoints": [8644], "characters": "⇄"}, "&RightCeiling;": {"codepoints": [8969], "characters": "⌉"}, "&RightDoubleBracket;": {"codepoints": [10215], "characters": "⟧"}, "&RightDownTeeVector;": {"codepoints": [10589], "characters": "⥝"}, "&RightDownVector;": {"codepoints": [8642], "characters": "⇂"}, "&RightDownVectorBar;": {"codepoints": [10581], "characters": "⥕"}, "&RightFloor;": {"codepoints": [8971], "characters": "⌋"}, "&RightTee;": {"codepoints": [8866], "characters": "⊢"}, "&RightTeeArrow;": {"codepoints": [8614], "characters": "↦"}, "&RightTeeVector;": {"codepoints": [10587], "characters": "⥛"}, "&RightTriangle;": {"codepoints": [8883], "characters": "⊳"}, "&RightTriangleBar;": {"codepoints": [10704], "characters": "⧐"}, "&RightTriangleEqual;": {"codepoints": [8885], "characters": "⊵"}, "&RightUpDownVector;": {"codepoints": [10575], "characters": "⥏"}, "&RightUpTeeVector;": {"codepoints": [10588], "characters": "⥜"}, "&RightUpVector;": {"codepoints": [8638], "characters": "↾"}, "&RightUpVectorBar;": {"codepoints": [10580], "characters": "⥔"}, "&RightVector;": {"codepoints": [8640], "characters": "⇀"}, "&RightVectorBar;": {"codepoints": [10579], "characters": "⥓"}, "&Rightarrow;": {"codepoints": [8658], "characters": "⇒"}, "&Ropf;": {"codepoints": [8477], "characters": "ℝ"}, "&RoundImplies;": {"codepoints": [10608], "characters": "⥰"}, "&Rrightarrow;": {"codepoints": [8667], "characters": "⇛"}, "&Rscr;": {"codepoints": [8475], "characters": "ℛ"}, "&Rsh;": {"codepoints": [8625], "characters": "↱"}, "&RuleDelayed;": {"codepoints": [10740], "characters": "⧴"}, "&SHCHcy;": {"codepoints": [1065], "characters": "Щ"}, "&SHcy;": {"codepoints": [1064], "characters": "Ш"}, "&SOFTcy;": {"codepoints": [1068], "characters": "Ь"}, "&Sacute;": {"codepoints": [346], "characters": "Ś"}, "&Sc;": {"codepoints": [10940], "characters": "⪼"}, "&Scaron;": {"codepoints": [352], "characters": "Š"}, "&Scedil;": {"codepoints": [350], "characters": "Ş"}, "&Scirc;": {"codepoints": [348], "characters": "Ŝ"}, "&Scy;": {"codepoints": [1057], "characters": "С"}, "&Sfr;": {"codepoints": [120086], "characters": "𝔖"}, "&ShortDownArrow;": {"codepoints": [8595], "characters": "↓"}, "&ShortLeftArrow;": {"codepoints": [8592], "characters": "←"}, "&ShortRightArrow;": {"codepoints": [8594], "characters": "→"}, "&ShortUpArrow;": {"codepoints": [8593], "characters": "↑"}, "&Sigma;": {"codepoints": [931], "characters": "Σ"}, "&SmallCircle;": {"codepoints": [8728], "characters": "∘"}, "&Sopf;": {"codepoints": [120138], "characters": "𝕊"}, "&Sqrt;": {"codepoints": [8730], "characters": "√"}, "&Square;": {"codepoints": [9633], "characters": "□"}, "&SquareIntersection;": {"codepoints": [8851], "characters": "⊓"}, "&SquareSubset;": {"codepoints": [8847], "characters": "⊏"}, "&SquareSubsetEqual;": {"codepoints": [8849], "characters": "⊑"}, "&SquareSuperset;": {"codepoints": [8848], "characters": "⊐"}, "&SquareSupersetEqual;": {"codepoints": [8850], "characters": "⊒"}, "&SquareUnion;": {"codepoints": [8852], "characters": "⊔"}, "&Sscr;": {"codepoints": [119982], "characters": "𝒮"}, "&Star;": {"codepoints": [8902], "characters": "⋆"}, "&Sub;": {"codepoints": [8912], "characters": "⋐"}, "&Subset;": {"codepoints": [8912], "characters": "⋐"}, "&SubsetEqual;": {"codepoints": [8838], "characters": "⊆"}, "&Succeeds;": {"codepoints": [8827], "characters": "≻"}, "&SucceedsEqual;": {"codepoints": [10928], "characters": "⪰"}, "&SucceedsSlantEqual;": {"codepoints": [8829], "characters": "≽"}, "&SucceedsTilde;": {"codepoints": [8831], "characters": "≿"}, "&SuchThat;": {"codepoints": [8715], "characters": "∋"}, "&Sum;": {"codepoints": [8721], "characters": "∑"}, "&Sup;": {"codepoints": [8913], "characters": "⋑"}, "&Superset;": {"codepoints": [8835], "characters": "⊃"}, "&SupersetEqual;": {"codepoints": [8839], "characters": "⊇"}, "&Supset;": {"codepoints": [8913], "characters": "⋑"}, "&THORN": {"codepoints": [222], "characters": "Þ"}, "&THORN;": {"codepoints": [222], "characters": "Þ"}, "&TRADE;": {"codepoints": [8482], "characters": "™"}, "&TSHcy;": {"codepoints": [1035], "characters": "Ћ"}, "&TScy;": {"codepoints": [1062], "characters": "Ц"}, "&Tab;": {"codepoints": [9], "characters": "\t"}, "&Tau;": {"codepoints": [932], "characters": "Τ"}, "&Tcaron;": {"codepoints": [356], "characters": "Ť"}, "&Tcedil;": {"codepoints": [354], "characters": "Ţ"}, "&Tcy;": {"codepoints": [1058], "characters": "Т"}, "&Tfr;": {"codepoints": [120087], "characters": "𝔗"}, "&Therefore;": {"codepoints": [8756], "characters": "∴"}, "&Theta;": {"codepoints": [920], "characters": "Θ"}, "&ThickSpace;": {"codepoints": [8287, 8202], "characters": "  "}, "&ThinSpace;": {"codepoints": [8201], "characters": " "}, "&Tilde;": {"codepoints": [8764], "characters": "∼"}, "&TildeEqual;": {"codepoints": [8771], "characters": "≃"}, "&TildeFullEqual;": {"codepoints": [8773], "characters": "≅"}, "&TildeTilde;": {"codepoints": [8776], "characters": "≈"}, "&Topf;": {"codepoints": [120139], "characters": "𝕋"}, "&TripleDot;": {"codepoints": [8411], "characters": "⃛"}, "&Tscr;": {"codepoints": [119983], "characters": "𝒯"}, "&Tstrok;": {"codepoints": [358], "characters": "Ŧ"}, "&Uacute": {"codepoints": [218], "characters": "Ú"}, "&Uacute;": {"codepoints": [218], "characters": "Ú"}, "&Uarr;": {"codepoints": [8607], "characters": "↟"}, "&Uarrocir;": {"codepoints": [10569], "characters": "⥉"}, "&Ubrcy;": {"codepoints": [1038], "characters": "Ў"}, "&Ubreve;": {"codepoints": [364], "characters": "Ŭ"}, "&Ucirc": {"codepoints": [219], "characters": "Û"}, "&Ucirc;": {"codepoints": [219], "characters": "Û"}, "&Ucy;": {"codepoints": [1059], "characters": "У"}, "&Udblac;": {"codepoints": [368], "characters": "Ű"}, "&Ufr;": {"codepoints": [120088], "characters": "𝔘"}, "&Ugrave": {"codepoints": [217], "characters": "Ù"}, "&Ugrave;": {"codepoints": [217], "characters": "Ù"}, "&Umacr;": {"codepoints": [362], "characters": "Ū"}, "&UnderBar;": {"codepoints": [95], "characters": "_"}, "&UnderBrace;": {"codepoints": [9183], "characters": "⏟"}, "&UnderBracket;": {"codepoints": [9141], "characters": "⎵"}, "&UnderParenthesis;": {"codepoints": [9181], "characters": "⏝"}, "&Union;": {"codepoints": [8899], "characters": "⋃"}, "&UnionPlus;": {"codepoints": [8846], "characters": "⊎"}, "&Uogon;": {"codepoints": [370], "characters": "Ų"}, "&Uopf;": {"codepoints": [120140], "characters": "𝕌"}, "&UpArrow;": {"codepoints": [8593], "characters": "↑"}, "&UpArrowBar;": {"codepoints": [10514], "characters": "⤒"}, "&UpArrowDownArrow;": {"codepoints": [8645], "characters": "⇅"}, "&UpDownArrow;": {"codepoints": [8597], "characters": "↕"}, "&UpEquilibrium;": {"codepoints": [10606], "characters": "⥮"}, "&UpTee;": {"codepoints": [8869], "characters": "⊥"}, "&UpTeeArrow;": {"codepoints": [8613], "characters": "↥"}, "&Uparrow;": {"codepoints": [8657], "characters": "⇑"}, "&Updownarrow;": {"codepoints": [8661], "characters": "⇕"}, "&UpperLeftArrow;": {"codepoints": [8598], "characters": "↖"}, "&UpperRightArrow;": {"codepoints": [8599], "characters": "↗"}, "&Upsi;": {"codepoints": [978], "characters": "ϒ"}, "&Upsilon;": {"codepoints": [933], "characters": "Υ"}, "&Uring;": {"codepoints": [366], "characters": "Ů"}, "&Uscr;": {"codepoints": [119984], "characters": "𝒰"}, "&Utilde;": {"codepoints": [360], "characters": "Ũ"}, "&Uuml": {"codepoints": [220], "characters": "Ü"}, "&Uuml;": {"codepoints": [220], "characters": "Ü"}, "&VDash;": {"codepoints": [8875], "characters": "⊫"}, "&Vbar;": {"codepoints": [10987], "characters": "⫫"}, "&Vcy;": {"codepoints": [1042], "characters": "В"}, "&Vdash;": {"codepoints": [8873], "characters": "⊩"}, "&Vdashl;": {"codepoints": [10982], "characters": "⫦"}, "&Vee;": {"codepoints": [8897], "characters": "⋁"}, "&Verbar;": {"codepoints": [8214], "characters": "‖"}, "&Vert;": {"codepoints": [8214], "characters": "‖"}, "&VerticalBar;": {"codepoints": [8739], "characters": "∣"}, "&VerticalLine;": {"codepoints": [124], "characters": "|"}, "&VerticalSeparator;": {"codepoints": [10072], "characters": "❘"}, "&VerticalTilde;": {"codepoints": [8768], "characters": "≀"}, "&VeryThinSpace;": {"codepoints": [8202], "characters": " "}, "&Vfr;": {"codepoints": [120089], "characters": "𝔙"}, "&Vopf;": {"codepoints": [120141], "characters": "𝕍"}, "&Vscr;": {"codepoints": [119985], "characters": "𝒱"}, "&Vvdash;": {"codepoints": [8874], "characters": "⊪"}, "&Wcirc;": {"codepoints": [372], "characters": "Ŵ"}, "&Wedge;": {"codepoints": [8896], "characters": "⋀"}, "&Wfr;": {"codepoints": [120090], "characters": "𝔚"}, "&Wopf;": {"codepoints": [120142], "characters": "𝕎"}, "&Wscr;": {"codepoints": [119986], "characters": "𝒲"}, "&Xfr;": {"codepoints": [120091], "characters": "𝔛"}, "&Xi;": {"codepoints": [926], "characters": "Ξ"}, "&Xopf;": {"codepoints": [120143], "characters": "𝕏"}, "&Xscr;": {"codepoints": [119987], "characters": "𝒳"}, "&YAcy;": {"codepoints": [1071], "characters": "Я"}, "&YIcy;": {"codepoints": [1031], "characters": "Ї"}, "&YUcy;": {"codepoints": [1070], "characters": "Ю"}, "&Yacute": {"codepoints": [221], "characters": "Ý"}, "&Yacute;": {"codepoints": [221], "characters": "Ý"}, "&Ycirc;": {"codepoints": [374], "characters": "Ŷ"}, "&Ycy;": {"codepoints": [1067], "characters": "Ы"}, "&Yfr;": {"codepoints": [120092], "characters": "𝔜"}, "&Yopf;": {"codepoints": [120144], "characters": "𝕐"}, "&Yscr;": {"codepoints": [119988], "characters": "𝒴"}, "&Yuml;": {"codepoints": [376], "characters": "Ÿ"}, "&ZHcy;": {"codepoints": [1046], "characters": "Ж"}, "&Zacute;": {"codepoints": [377], "characters": "Ź"}, "&Zcaron;": {"codepoints": [381], "characters": "Ž"}, "&Zcy;": {"codepoints": [1047], "characters": "З"}, "&Zdot;": {"codepoints": [379], "characters": "Ż"}, "&ZeroWidthSpace;": {"codepoints": [8203], "characters": "​"}, "&Zeta;": {"codepoints": [918], "characters": "Ζ"}, "&Zfr;": {"codepoints": [8488], "characters": "ℨ"}, "&Zopf;": {"codepoints": [8484], "characters": "ℤ"}, "&Zscr;": {"codepoints": [119989], "characters": "𝒵"}, "&aacute": {"codepoints": [225], "characters": "á"}, "&aacute;": {"codepoints": [225], "characters": "á"}, "&abreve;": {"codepoints": [259], "characters": "ă"}, "&ac;": {"codepoints": [8766], "characters": "∾"}, "&acE;": {"codepoints": [8766, 819], "characters": "∾̳"}, "&acd;": {"codepoints": [8767], "characters": "∿"}, "&acirc": {"codepoints": [226], "characters": "â"}, "&acirc;": {"codepoints": [226], "characters": "â"}, "&acute": {"codepoints": [180], "characters": "´"}, "&acute;": {"codepoints": [180], "characters": "´"}, "&acy;": {"codepoints": [1072], "characters": "а"}, "&aelig": {"codepoints": [230], "characters": "æ"}, "&aelig;": {"codepoints": [230], "characters": "æ"}, "&af;": {"codepoints": [8289], "characters": "⁡"}, "&afr;": {"codepoints": [120094], "characters": "𝔞"}, "&agrave": {"codepoints": [224], "characters": "à"}, "&agrave;": {"codepoints": [224], "characters": "à"}, "&alefsym;": {"codepoints": [8501], "characters": "ℵ"}, "&aleph;": {"codepoints": [8501], "characters": "ℵ"}, "&alpha;": {"codepoints": [945], "characters": "α"}, "&amacr;": {"codepoints": [257], "characters": "ā"}, "&amalg;": {"codepoints": [10815], "characters": "⨿"}, "&amp": {"codepoints": [38], "characters": "&"}, "&amp;": {"codepoints": [38], "characters": "&"}, "&and;": {"codepoints": [8743], "characters": "∧"}, "&andand;": {"codepoints": [10837], "characters": "⩕"}, "&andd;": {"codepoints": [10844], "characters": "⩜"}, "&andslope;": {"codepoints": [10840], "characters": "⩘"}, "&andv;": {"codepoints": [10842], "characters": "⩚"}, "&ang;": {"codepoints": [8736], "characters": "∠"}, "&ange;": {"codepoints": [10660], "characters": "⦤"}, "&angle;": {"codepoints": [8736], "characters": "∠"}, "&angmsd;": {"codepoints": [8737], "characters": "∡"}, "&angmsdaa;": {"codepoints": [10664], "characters": "⦨"}, "&angmsdab;": {"codepoints": [10665], "characters": "⦩"}, "&angmsdac;": {"codepoints": [10666], "characters": "⦪"}, "&angmsdad;": {"codepoints": [10667], "characters": "⦫"}, "&angmsdae;": {"codepoints": [10668], "characters": "⦬"}, "&angmsdaf;": {"codepoints": [10669], "characters": "⦭"}, "&angmsdag;": {"codepoints": [10670], "characters": "⦮"}, "&angmsdah;": {"codepoints": [10671], "characters": "⦯"}, "&angrt;": {"codepoints": [8735], "characters": "∟"}, "&angrtvb;": {"codepoints": [8894], "characters": "⊾"}, "&angrtvbd;": {"codepoints": [10653], "characters": "⦝"}, "&angsph;": {"codepoints": [8738], "characters": "∢"}, "&angst;": {"codepoints": [197], "characters": "Å"}, "&angzarr;": {"codepoints": [9084], "characters": "⍼"}, "&aogon;": {"codepoints": [261], "characters": "ą"}, "&aopf;": {"codepoints": [120146], "characters": "𝕒"}, "&ap;": {"codepoints": [8776], "characters": "≈"}, "&apE;": {"codepoints": [10864], "characters": "⩰"}, "&apacir;": {"codepoints": [10863], "characters": "⩯"}, "&ape;": {"codepoints": [8778], "characters": "≊"}, "&apid;": {"codepoints": [8779], "characters": "≋"}, "&apos;": {"codepoints": [39], "characters": "'"}, "&approx;": {"codepoints": [8776], "characters": "≈"}, "&approxeq;": {"codepoints": [8778], "characters": "≊"}, "&aring": {"codepoints": [229], "characters": "å"}, "&aring;": {"codepoints": [229], "characters": "å"}, "&ascr;": {"codepoints": [119990], "characters": "𝒶"}, "&ast;": {"codepoints": [42], "characters": "*"}, "&asymp;": {"codepoints": [8776], "characters": "≈"}, "&asympeq;": {"codepoints": [8781], "characters": "≍"}, "&atilde": {"codepoints": [227], "characters": "ã"}, "&atilde;": {"codepoints": [227], "characters": "ã"}, "&auml": {"codepoints": [228], "characters": "ä"}, "&auml;": {"codepoints": [228], "characters": "ä"}, "&awconint;": {"codepoints": [8755], "characters": "∳"}, "&awint;": {"codepoints": [10769], "characters": "⨑"}, "&bNot;": {"codepoints": [10989], "characters": "⫭"}, "&backcong;": {"codepoints": [8780], "characters": "≌"}, "&backepsilon;": {"codepoints": [1014], "characters": "϶"}, "&backprime;": {"codepoints": [8245], "characters": "‵"}, "&backsim;": {"codepoints": [8765], "characters": "∽"}, "&backsimeq;": {"codepoints": [8909], "characters": "⋍"}, "&barvee;": {"codepoints": [8893], "characters": "⊽"}, "&barwed;": {"codepoints": [8965], "characters": "⌅"}, "&barwedge;": {"codepoints": [8965], "characters": "⌅"}, "&bbrk;": {"codepoints": [9141], "characters": "⎵"}, "&bbrktbrk;": {"codepoints": [9142], "characters": "⎶"}, "&bcong;": {"codepoints": [8780], "characters": "≌"}, "&bcy;": {"codepoints": [1073], "characters": "б"}, "&bdquo;": {"codepoints": [8222], "characters": "„"}, "&becaus;": {"codepoints": [8757], "characters": "∵"}, "&because;": {"codepoints": [8757], "characters": "∵"}, "&bemptyv;": {"codepoints": [10672], "characters": "⦰"}, "&bepsi;": {"codepoints": [1014], "characters": "϶"}, "&bernou;": {"codepoints": [8492], "characters": "ℬ"}, "&beta;": {"codepoints": [946], "characters": "β"}, "&beth;": {"codepoints": [8502], "characters": "ℶ"}, "&between;": {"codepoints": [8812], "characters": "≬"}, "&bfr;": {"codepoints": [120095], "characters": "𝔟"}, "&bigcap;": {"codepoints": [8898], "characters": "⋂"}, "&bigcirc;": {"codepoints": [9711], "characters": "◯"}, "&bigcup;": {"codepoints": [8899], "characters": "⋃"}, "&bigodot;": {"codepoints": [10752], "characters": "⨀"}, "&bigoplus;": {"codepoints": [10753], "characters": "⨁"}, "&bigotimes;": {"codepoints": [10754], "characters": "⨂"}, "&bigsqcup;": {"codepoints": [10758], "characters": "⨆"}, "&bigstar;": {"codepoints": [9733], "characters": "★"}, "&bigtriangledown;": {"codepoints": [9661], "characters": "▽"}, "&bigtriangleup;": {"codepoints": [9651], "characters": "△"}, "&biguplus;": {"codepoints": [10756], "characters": "⨄"}, "&bigvee;": {"codepoints": [8897], "characters": "⋁"}, "&bigwedge;": {"codepoints": [8896], "characters": "⋀"}, "&bkarow;": {"codepoints": [10509], "characters": "⤍"}, "&blacklozenge;": {"codepoints": [10731], "characters": "⧫"}, "&blacksquare;": {"codepoints": [9642], "characters": "▪"}, "&blacktriangle;": {"codepoints": [9652], "characters": "▴"}, "&blacktriangledown;": {"codepoints": [9662], "characters": "▾"}, "&blacktriangleleft;": {"codepoints": [9666], "characters": "◂"}, "&blacktriangleright;": {"codepoints": [9656], "characters": "▸"}, "&blank;": {"codepoints": [9251], "characters": "␣"}, "&blk12;": {"codepoints": [9618], "characters": "▒"}, "&blk14;": {"codepoints": [9617], "characters": "░"}, "&blk34;": {"codepoints": [9619], "characters": "▓"}, "&block;": {"codepoints": [9608], "characters": "█"}, "&bne;": {"codepoints": [61, 8421], "characters": "=⃥"}, "&bnequiv;": {"codepoints": [8801, 8421], "characters": "≡⃥"}, "&bnot;": {"codepoints": [8976], "characters": "⌐"}, "&bopf;": {"codepoints": [120147], "characters": "𝕓"}, "&bot;": {"codepoints": [8869], "characters": "⊥"}, "&bottom;": {"codepoints": [8869], "characters": "⊥"}, "&bowtie;": {"codepoints": [8904], "characters": "⋈"}, "&boxDL;": {"codepoints": [9559], "characters": "╗"}, "&boxDR;": {"codepoints": [9556], "characters": "╔"}, "&boxDl;": {"codepoints": [9558], "characters": "╖"}, "&boxDr;": {"codepoints": [9555], "characters": "╓"}, "&boxH;": {"codepoints": [9552], "characters": "═"}, "&boxHD;": {"codepoints": [9574], "characters": "╦"}, "&boxHU;": {"codepoints": [9577], "characters": "╩"}, "&boxHd;": {"codepoints": [9572], "characters": "╤"}, "&boxHu;": {"codepoints": [9575], "characters": "╧"}, "&boxUL;": {"codepoints": [9565], "characters": "╝"}, "&boxUR;": {"codepoints": [9562], "characters": "╚"}, "&boxUl;": {"codepoints": [9564], "characters": "╜"}, "&boxUr;": {"codepoints": [9561], "characters": "╙"}, "&boxV;": {"codepoints": [9553], "characters": "║"}, "&boxVH;": {"codepoints": [9580], "characters": "╬"}, "&boxVL;": {"codepoints": [9571], "characters": "╣"}, "&boxVR;": {"codepoints": [9568], "characters": "╠"}, "&boxVh;": {"codepoints": [9579], "characters": "╫"}, "&boxVl;": {"codepoints": [9570], "characters": "╢"}, "&boxVr;": {"codepoints": [9567], "characters": "╟"}, "&boxbox;": {"codepoints": [10697], "characters": "⧉"}, "&boxdL;": {"codepoints": [9557], "characters": "╕"}, "&boxdR;": {"codepoints": [9554], "characters": "╒"}, "&boxdl;": {"codepoints": [9488], "characters": "┐"}, "&boxdr;": {"codepoints": [9484], "characters": "┌"}, "&boxh;": {"codepoints": [9472], "characters": "─"}, "&boxhD;": {"codepoints": [9573], "characters": "╥"}, "&boxhU;": {"codepoints": [9576], "characters": "╨"}, "&boxhd;": {"codepoints": [9516], "characters": "┬"}, "&boxhu;": {"codepoints": [9524], "characters": "┴"}, "&boxminus;": {"codepoints": [8863], "characters": "⊟"}, "&boxplus;": {"codepoints": [8862], "characters": "⊞"}, "&boxtimes;": {"codepoints": [8864], "characters": "⊠"}, "&boxuL;": {"codepoints": [9563], "characters": "╛"}, "&boxuR;": {"codepoints": [9560], "characters": "╘"}, "&boxul;": {"codepoints": [9496], "characters": "┘"}, "&boxur;": {"codepoints": [9492], "characters": "└"}, "&boxv;": {"codepoints": [9474], "characters": "│"}, "&boxvH;": {"codepoints": [9578], "characters": "╪"}, "&boxvL;": {"codepoints": [9569], "characters": "╡"}, "&boxvR;": {"codepoints": [9566], "characters": "╞"}, "&boxvh;": {"codepoints": [9532], "characters": "┼"}, "&boxvl;": {"codepoints": [9508], "characters": "┤"}, "&boxvr;": {"codepoints": [9500], "characters": "├"}, "&bprime;": {"codepoints": [8245], "characters": "‵"}, "&breve;": {"codepoints": [728], "characters": "˘"}, "&brvbar": {"codepoints": [166], "characters": "¦"}, "&brvbar;": {"codepoints": [166], "characters": "¦"}, "&bscr;": {"codepoints": [119991], "characters": "𝒷"}, "&bsemi;": {"codepoints": [8271], "characters": "⁏"}, "&bsim;": {"codepoints": [8765], "characters": "∽"}, "&bsime;": {"codepoints": [8909], "characters": "⋍"}, "&bsol;": {"codepoints": [92], "characters": "\\"}, "&bsolb;": {"codepoints": [10693], "characters": "⧅"}, "&bsolhsub;": {"codepoints": [10184], "characters": "⟈"}, "&bull;": {"codepoints": [8226], "characters": "•"}, "&bullet;": {"codepoints": [8226], "characters": "•"}, "&bump;": {"codepoints": [8782], "characters": "≎"}, "&bumpE;": {"codepoints": [10926], "characters": "⪮"}, "&bumpe;": {"codepoints": [8783], "characters": "≏"}, "&bumpeq;": {"codepoints": [8783], "characters": "≏"}, "&cacute;": {"codepoints": [263], "characters": "ć"}, "&cap;": {"codepoints": [8745], "characters": "∩"}, "&capand;": {"codepoints": [10820], "characters": "⩄"}, "&capbrcup;": {"codepoints": [10825], "characters": "⩉"}, "&capcap;": {"codepoints": [10827], "characters": "⩋"}, "&capcup;": {"codepoints": [10823], "characters": "⩇"}, "&capdot;": {"codepoints": [10816], "characters": "⩀"}, "&caps;": {"codepoints": [8745, 65024], "characters": "∩︀"}, "&caret;": {"codepoints": [8257], "characters": "⁁"}, "&caron;": {"codepoints": [711], "characters": "ˇ"}, "&ccaps;": {"codepoints": [10829], "characters": "⩍"}, "&ccaron;": {"codepoints": [269], "characters": "č"}, "&ccedil": {"codepoints": [231], "characters": "ç"}, "&ccedil;": {"codepoints": [231], "characters": "ç"}, "&ccirc;": {"codepoints": [265], "characters": "ĉ"}, "&ccups;": {"codepoints": [10828], "characters": "⩌"}, "&ccupssm;": {"codepoints": [10832], "characters": "⩐"}, "&cdot;": {"codepoints": [267], "characters": "ċ"}, "&cedil": {"codepoints": [184], "characters": "¸"}, "&cedil;": {"codepoints": [184], "characters": "¸"}, "&cemptyv;": {"codepoints": [10674], "characters": "⦲"}, "&cent": {"codepoints": [162], "characters": "¢"}, "&cent;": {"codepoints": [162], "characters": "¢"}, "&centerdot;": {"codepoints": [183], "characters": "·"}, "&cfr;": {"codepoints": [120096], "characters": "𝔠"}, "&chcy;": {"codepoints": [1095], "characters": "ч"}, "&check;": {"codepoints": [10003], "characters": "✓"}, "&checkmark;": {"codepoints": [10003], "characters": "✓"}, "&chi;": {"codepoints": [967], "characters": "χ"}, "&cir;": {"codepoints": [9675], "characters": "○"}, "&cirE;": {"codepoints": [10691], "characters": "⧃"}, "&circ;": {"codepoints": [710], "characters": "ˆ"}, "&circeq;": {"codepoints": [8791], "characters": "≗"}, "&circlearrowleft;": {"codepoints": [8634], "characters": "↺"}, "&circlearrowright;": {"codepoints": [8635], "characters": "↻"}, "&circledR;": {"codepoints": [174], "characters": "®"}, "&circledS;": {"codepoints": [9416], "characters": "Ⓢ"}, "&circledast;": {"codepoints": [8859], "characters": "⊛"}, "&circledcirc;": {"codepoints": [8858], "characters": "⊚"}, "&circleddash;": {"codepoints": [8861], "characters": "⊝"}, "&cire;": {"codepoints": [8791], "characters": "≗"}, "&cirfnint;": {"codepoints": [10768], "characters": "⨐"}, "&cirmid;": {"codepoints": [10991], "characters": "⫯"}, "&cirscir;": {"codepoints": [10690], "characters": "⧂"}, "&clubs;": {"codepoints": [9827], "characters": "♣"}, "&clubsuit;": {"codepoints": [9827], "characters": "♣"}, "&colon;": {"codepoints": [58], "characters": ":"}, "&colone;": {"codepoints": [8788], "characters": "≔"}, "&coloneq;": {"codepoints": [8788], "characters": "≔"}, "&comma;": {"codepoints": [44], "characters": ","}, "&commat;": {"codepoints": [64], "characters": "@"}, "&comp;": {"codepoints": [8705], "characters": "∁"}, "&compfn;": {"codepoints": [8728], "characters": "∘"}, "&complement;": {"codepoints": [8705], "characters": "∁"}, "&complexes;": {"codepoints": [8450], "characters": "ℂ"}, "&cong;": {"codepoints": [8773], "characters": "≅"}, "&congdot;": {"codepoints": [10861], "characters": "⩭"}, "&conint;": {"codepoints": [8750], "characters": "∮"}, "&copf;": {"codepoints": [120148], "characters": "𝕔"}, "&coprod;": {"codepoints": [8720], "characters": "∐"}, "&copy": {"codepoints": [169], "characters": "©"}, "&copy;": {"codepoints": [169], "characters": "©"}, "&copysr;": {"codepoints": [8471], "characters": "℗"}, "&crarr;": {"codepoints": [8629], "characters": "↵"}, "&cross;": {"codepoints": [10007], "characters": "✗"}, "&cscr;": {"codepoints": [119992], "characters": "𝒸"}, "&csub;": {"codepoints": [10959], "characters": "⫏"}, "&csube;": {"codepoints": [10961], "characters": "⫑"}, "&csup;": {"codepoints": [10960], "characters": "⫐"}, "&csupe;": {"codepoints": [10962], "characters": "⫒"}, "&ctdot;": {"codepoints": [8943], "characters": "⋯"}, "&cudarrl;": {"codepoints": [10552], "characters": "⤸"}, "&cudarrr;": {"codepoints": [10549], "characters": "⤵"}, "&cuepr;": {"codepoints": [8926], "characters": "⋞"}, "&cuesc;": {"codepoints": [8927], "characters": "⋟"}, "&cularr;": {"codepoints": [8630], "characters": "↶"}, "&cularrp;": {"codepoints": [10557], "characters": "⤽"}, "&cup;": {"codepoints": [8746], "characters": "∪"}, "&cupbrcap;": {"codepoints": [10824], "characters": "⩈"}, "&cupcap;": {"codepoints": [10822], "characters": "⩆"}, "&cupcup;": {"codepoints": [10826], "characters": "⩊"}, "&cupdot;": {"codepoints": [8845], "characters": "⊍"}, "&cupor;": {"codepoints": [10821], "characters": "⩅"}, "&cups;": {"codepoints": [8746, 65024], "characters": "∪︀"}, "&curarr;": {"codepoints": [8631], "characters": "↷"}, "&curarrm;": {"codepoints": [10556], "characters": "⤼"}, "&curlyeqprec;": {"codepoints": [8926], "characters": "⋞"}, "&curlyeqsucc;": {"codepoints": [8927], "characters": "⋟"}, "&curlyvee;": {"codepoints": [8910], "characters": "⋎"}, "&curlywedge;": {"codepoints": [8911], "characters": "⋏"}, "&curren": {"codepoints": [164], "characters": "¤"}, "&curren;": {"codepoints": [164], "characters": "¤"}, "&curvearrowleft;": {"codepoints": [8630], "characters": "↶"}, "&curvearrowright;": {"codepoints": [8631], "characters": "↷"}, "&cuvee;": {"codepoints": [8910], "characters": "⋎"}, "&cuwed;": {"codepoints": [8911], "characters": "⋏"}, "&cwconint;": {"codepoints": [8754], "characters": "∲"}, "&cwint;": {"codepoints": [8753], "characters": "∱"}, "&cylcty;": {"codepoints": [9005], "characters": "⌭"}, "&dArr;": {"codepoints": [8659], "characters": "⇓"}, "&dHar;": {"codepoints": [10597], "characters": "⥥"}, "&dagger;": {"codepoints": [8224], "characters": "†"}, "&daleth;": {"codepoints": [8504], "characters": "ℸ"}, "&darr;": {"codepoints": [8595], "characters": "↓"}, "&dash;": {"codepoints": [8208], "characters": "‐"}, "&dashv;": {"codepoints": [8867], "characters": "⊣"}, "&dbkarow;": {"codepoints": [10511], "characters": "⤏"}, "&dblac;": {"codepoints": [733], "characters": "˝"}, "&dcaron;": {"codepoints": [271], "characters": "ď"}, "&dcy;": {"codepoints": [1076], "characters": "д"}, "&dd;": {"codepoints": [8518], "characters": "ⅆ"}, "&ddagger;": {"codepoints": [8225], "characters": "‡"}, "&ddarr;": {"codepoints": [8650], "characters": "⇊"}, "&ddotseq;": {"codepoints": [10871], "characters": "⩷"}, "&deg": {"codepoints": [176], "characters": "°"}, "&deg;": {"codepoints": [176], "characters": "°"}, "&delta;": {"codepoints": [948], "characters": "δ"}, "&demptyv;": {"codepoints": [10673], "characters": "⦱"}, "&dfisht;": {"codepoints": [10623], "characters": "⥿"}, "&dfr;": {"codepoints": [120097], "characters": "𝔡"}, "&dharl;": {"codepoints": [8643], "characters": "⇃"}, "&dharr;": {"codepoints": [8642], "characters": "⇂"}, "&diam;": {"codepoints": [8900], "characters": "⋄"}, "&diamond;": {"codepoints": [8900], "characters": "⋄"}, "&diamondsuit;": {"codepoints": [9830], "characters": "♦"}, "&diams;": {"codepoints": [9830], "characters": "♦"}, "&die;": {"codepoints": [168], "characters": "¨"}, "&digamma;": {"codepoints": [989], "characters": "ϝ"}, "&disin;": {"codepoints": [8946], "characters": "⋲"}, "&div;": {"codepoints": [247], "characters": "÷"}, "&divide": {"codepoints": [247], "characters": "÷"}, "&divide;": {"codepoints": [247], "characters": "÷"}, "&divideontimes;": {"codepoints": [8903], "characters": "⋇"}, "&divonx;": {"codepoints": [8903], "characters": "⋇"}, "&djcy;": {"codepoints": [1106], "characters": "ђ"}, "&dlcorn;": {"codepoints": [8990], "characters": "⌞"}, "&dlcrop;": {"codepoints": [8973], "characters": "⌍"}, "&dollar;": {"codepoints": [36], "characters": "$"}, "&dopf;": {"codepoints": [120149], "characters": "𝕕"}, "&dot;": {"codepoints": [729], "characters": "˙"}, "&doteq;": {"codepoints": [8784], "characters": "≐"}, "&doteqdot;": {"codepoints": [8785], "characters": "≑"}, "&dotminus;": {"codepoints": [8760], "characters": "∸"}, "&dotplus;": {"codepoints": [8724], "characters": "∔"}, "&dotsquare;": {"codepoints": [8865], "characters": "⊡"}, "&doublebarwedge;": {"codepoints": [8966], "characters": "⌆"}, "&downarrow;": {"codepoints": [8595], "characters": "↓"}, "&downdownarrows;": {"codepoints": [8650], "characters": "⇊"}, "&downharpoonleft;": {"codepoints": [8643], "characters": "⇃"}, "&downharpoonright;": {"codepoints": [8642], "characters": "⇂"}, "&drbkarow;": {"codepoints": [10512], "characters": "⤐"}, "&drcorn;": {"codepoints": [8991], "characters": "⌟"}, "&drcrop;": {"codepoints": [8972], "characters": "⌌"}, "&dscr;": {"codepoints": [119993], "characters": "𝒹"}, "&dscy;": {"codepoints": [1109], "characters": "ѕ"}, "&dsol;": {"codepoints": [10742], "characters": "⧶"}, "&dstrok;": {"codepoints": [273], "characters": "đ"}, "&dtdot;": {"codepoints": [8945], "characters": "⋱"}, "&dtri;": {"codepoints": [9663], "characters": "▿"}, "&dtrif;": {"codepoints": [9662], "characters": "▾"}, "&duarr;": {"codepoints": [8693], "characters": "⇵"}, "&duhar;": {"codepoints": [10607], "characters": "⥯"}, "&dwangle;": {"codepoints": [10662], "characters": "⦦"}, "&dzcy;": {"codepoints": [1119], "characters": "џ"}, "&dzigrarr;": {"codepoints": [10239], "characters": "⟿"}, "&eDDot;": {"codepoints": [10871], "characters": "⩷"}, "&eDot;": {"codepoints": [8785], "characters": "≑"}, "&eacute": {"codepoints": [233], "characters": "é"}, "&eacute;": {"codepoints": [233], "characters": "é"}, "&easter;": {"codepoints": [10862], "characters": "⩮"}, "&ecaron;": {"codepoints": [283], "characters": "ě"}, "&ecir;": {"codepoints": [8790], "characters": "≖"}, "&ecirc": {"codepoints": [234], "characters": "ê"}, "&ecirc;": {"codepoints": [234], "characters": "ê"}, "&ecolon;": {"codepoints": [8789], "characters": "≕"}, "&ecy;": {"codepoints": [1101], "characters": "э"}, "&edot;": {"codepoints": [279], "characters": "ė"}, "&ee;": {"codepoints": [8519], "characters": "ⅇ"}, "&efDot;": {"codepoints": [8786], "characters": "≒"}, "&efr;": {"codepoints": [120098], "characters": "𝔢"}, "&eg;": {"codepoints": [10906], "characters": "⪚"}, "&egrave": {"codepoints": [232], "characters": "è"}, "&egrave;": {"codepoints": [232], "characters": "è"}, "&egs;": {"codepoints": [10902], "characters": "⪖"}, "&egsdot;": {"codepoints": [10904], "characters": "⪘"}, "&el;": {"codepoints": [10905], "characters": "⪙"}, "&elinters;": {"codepoints": [9191], "characters": "⏧"}, "&ell;": {"codepoints": [8467], "characters": "ℓ"}, "&els;": {"codepoints": [10901], "characters": "⪕"}, "&elsdot;": {"codepoints": [10903], "characters": "⪗"}, "&emacr;": {"codepoints": [275], "characters": "ē"}, "&empty;": {"codepoints": [8709], "characters": "∅"}, "&emptyset;": {"codepoints": [8709], "characters": "∅"}, "&emptyv;": {"codepoints": [8709], "characters": "∅"}, "&emsp13;": {"codepoints": [8196], "characters": " "}, "&emsp14;": {"codepoints": [8197], "characters": " "}, "&emsp;": {"codepoints": [8195], "characters": " "}, "&eng;": {"codepoints": [331], "characters": "ŋ"}, "&ensp;": {"codepoints": [8194], "characters": " "}, "&eogon;": {"codepoints": [281], "characters": "ę"}, "&eopf;": {"codepoints": [120150], "characters": "𝕖"}, "&epar;": {"codepoints": [8917], "characters": "⋕"}, "&eparsl;": {"codepoints": [10723], "characters": "⧣"}, "&eplus;": {"codepoints": [10865], "characters": "⩱"}, "&epsi;": {"codepoints": [949], "characters": "ε"}, "&epsilon;": {"codepoints": [949], "characters": "ε"}, "&epsiv;": {"codepoints": [1013], "characters": "ϵ"}, "&eqcirc;": {"codepoints": [8790], "characters": "≖"}, "&eqcolon;": {"codepoints": [8789], "characters": "≕"}, "&eqsim;": {"codepoints": [8770], "characters": "≂"}, "&eqslantgtr;": {"codepoints": [10902], "characters": "⪖"}, "&eqslantless;": {"codepoints": [10901], "characters": "⪕"}, "&equals;": {"codepoints": [61], "characters": "="}, "&equest;": {"codepoints": [8799], "characters": "≟"}, "&equiv;": {"codepoints": [8801], "characters": "≡"}, "&equivDD;": {"codepoints": [10872], "characters": "⩸"}, "&eqvparsl;": {"codepoints": [10725], "characters": "⧥"}, "&erDot;": {"codepoints": [8787], "characters": "≓"}, "&erarr;": {"codepoints": [10609], "characters": "⥱"}, "&escr;": {"codepoints": [8495], "characters": "ℯ"}, "&esdot;": {"codepoints": [8784], "characters": "≐"}, "&esim;": {"codepoints": [8770], "characters": "≂"}, "&eta;": {"codepoints": [951], "characters": "η"}, "&eth": {"codepoints": [240], "characters": "ð"}, "&eth;": {"codepoints": [240], "characters": "ð"}, "&euml": {"codepoints": [235], "characters": "ë"}, "&euml;": {"codepoints": [235], "characters": "ë"}, "&euro;": {"codepoints": [8364], "characters": "€"}, "&excl;": {"codepoints": [33], "characters": "!"}, "&exist;": {"codepoints": [8707], "characters": "∃"}, "&expectation;": {"codepoints": [8496], "characters": "ℰ"}, "&exponentiale;": {"codepoints": [8519], "characters": "ⅇ"}, "&fallingdotseq;": {"codepoints": [8786], "characters": "≒"}, "&fcy;": {"codepoints": [1092], "characters": "ф"}, "&female;": {"codepoints": [9792], "characters": "♀"}, "&ffilig;": {"codepoints": [64259], "characters": "ﬃ"}, "&fflig;": {"codepoints": [64256], "characters": "ﬀ"}, "&ffllig;": {"codepoints": [64260], "characters": "ﬄ"}, "&ffr;": {"codepoints": [120099], "characters": "𝔣"}, "&filig;": {"codepoints": [64257], "characters": "ﬁ"}, "&fjlig;": {"codepoints": [102, 106], "characters": "fj"}, "&flat;": {"codepoints": [9837], "characters": "♭"}, "&fllig;": {"codepoints": [64258], "characters": "ﬂ"}, "&fltns;": {"codepoints": [9649], "characters": "▱"}, "&fnof;": {"codepoints": [402], "characters": "ƒ"}, "&fopf;": {"codepoints": [120151], "characters": "𝕗"}, "&forall;": {"codepoints": [8704], "characters": "∀"}, "&fork;": {"codepoints": [8916], "characters": "⋔"}, "&forkv;": {"codepoints": [10969], "characters": "⫙"}, "&fpartint;": {"codepoints": [10765], "characters": "⨍"}, "&frac12": {"codepoints": [189], "characters": "½"}, "&frac12;": {"codepoints": [189], "characters": "½"}, "&frac13;": {"codepoints": [8531], "characters": "⅓"}, "&frac14": {"codepoints": [188], "characters": "¼"}, "&frac14;": {"codepoints": [188], "characters": "¼"}, "&frac15;": {"codepoints": [8533], "characters": "⅕"}, "&frac16;": {"codepoints": [8537], "characters": "⅙"}, "&frac18;": {"codepoints": [8539], "characters": "⅛"}, "&frac23;": {"codepoints": [8532], "characters": "⅔"}, "&frac25;": {"codepoints": [8534], "characters": "⅖"}, "&frac34": {"codepoints": [190], "characters": "¾"}, "&frac34;": {"codepoints": [190], "characters": "¾"}, "&frac35;": {"codepoints": [8535], "characters": "⅗"}, "&frac38;": {"codepoints": [8540], "characters": "⅜"}, "&frac45;": {"codepoints": [8536], "characters": "⅘"}, "&frac56;": {"codepoints": [8538], "characters": "⅚"}, "&frac58;": {"codepoints": [8541], "characters": "⅝"}, "&frac78;": {"codepoints": [8542], "characters": "⅞"}, "&frasl;": {"codepoints": [8260], "characters": "⁄"}, "&frown;": {"codepoints": [8994], "characters": "⌢"}, "&fscr;": {"codepoints": [119995], "characters": "𝒻"}, "&gE;": {"codepoints": [8807], "characters": "≧"}, "&gEl;": {"codepoints": [10892], "characters": "⪌"}, "&gacute;": {"codepoints": [501], "characters": "ǵ"}, "&gamma;": {"codepoints": [947], "characters": "γ"}, "&gammad;": {"codepoints": [989], "characters": "ϝ"}, "&gap;": {"codepoints": [10886], "characters": "⪆"}, "&gbreve;": {"codepoints": [287], "characters": "ğ"}, "&gcirc;": {"codepoints": [285], "characters": "ĝ"}, "&gcy;": {"codepoints": [1075], "characters": "г"}, "&gdot;": {"codepoints": [289], "characters": "ġ"}, "&ge;": {"codepoints": [8805], "characters": "≥"}, "&gel;": {"codepoints": [8923], "characters": "⋛"}, "&geq;": {"codepoints": [8805], "characters": "≥"}, "&geqq;": {"codepoints": [8807], "characters": "≧"}, "&geqslant;": {"codepoints": [10878], "characters": "⩾"}, "&ges;": {"codepoints": [10878], "characters": "⩾"}, "&gescc;": {"codepoints": [10921], "characters": "⪩"}, "&gesdot;": {"codepoints": [10880], "characters": "⪀"}, "&gesdoto;": {"codepoints": [10882], "characters": "⪂"}, "&gesdotol;": {"codepoints": [10884], "characters": "⪄"}, "&gesl;": {"codepoints": [8923, 65024], "characters": "⋛︀"}, "&gesles;": {"codepoints": [10900], "characters": "⪔"}, "&gfr;": {"codepoints": [120100], "characters": "𝔤"}, "&gg;": {"codepoints": [8811], "characters": "≫"}, "&ggg;": {"codepoints": [8921], "characters": "⋙"}, "&gimel;": {"codepoints": [8503], "characters": "ℷ"}, "&gjcy;": {"codepoints": [1107], "characters": "ѓ"}, "&gl;": {"codepoints": [8823], "characters": "≷"}, "&glE;": {"codepoints": [10898], "characters": "⪒"}, "&gla;": {"codepoints": [10917], "characters": "⪥"}, "&glj;": {"codepoints": [10916], "characters": "⪤"}, "&gnE;": {"codepoints": [8809], "characters": "≩"}, "&gnap;": {"codepoints": [10890], "characters": "⪊"}, "&gnapprox;": {"codepoints": [10890], "characters": "⪊"}, "&gne;": {"codepoints": [10888], "characters": "⪈"}, "&gneq;": {"codepoints": [10888], "characters": "⪈"}, "&gneqq;": {"codepoints": [8809], "characters": "≩"}, "&gnsim;": {"codepoints": [8935], "characters": "⋧"}, "&gopf;": {"codepoints": [120152], "characters": "𝕘"}, "&grave;": {"codepoints": [96], "characters": "`"}, "&gscr;": {"codepoints": [8458], "characters": "ℊ"}, "&gsim;": {"codepoints": [8819], "characters": "≳"}, "&gsime;": {"codepoints": [10894], "characters": "⪎"}, "&gsiml;": {"codepoints": [10896], "characters": "⪐"}, "&gt": {"codepoints": [62], "characters": ">"}, "&gt;": {"codepoints": [62], "characters": ">"}, "&gtcc;": {"codepoints": [10919], "characters": "⪧"}, "&gtcir;": {"codepoints": [10874], "characters": "⩺"}, "&gtdot;": {"codepoints": [8919], "characters": "⋗"}, "&gtlPar;": {"codepoints": [10645], "characters": "⦕"}, "&gtquest;": {"codepoints": [10876], "characters": "⩼"}, "&gtrapprox;": {"codepoints": [10886], "characters": "⪆"}, "&gtrarr;": {"codepoints": [10616], "characters": "⥸"}, "&gtrdot;": {"codepoints": [8919], "characters": "⋗"}, "&gtreqless;": {"codepoints": [8923], "characters": "⋛"}, "&gtreqqless;": {"codepoints": [10892], "characters": "⪌"}, "&gtrless;": {"codepoints": [8823], "characters": "≷"}, "&gtrsim;": {"codepoints": [8819], "characters": "≳"}, "&gvertneqq;": {"codepoints": [8809, 65024], "characters": "≩︀"}, "&gvnE;": {"codepoints": [8809, 65024], "characters": "≩︀"}, "&hArr;": {"codepoints": [8660], "characters": "⇔"}, "&hairsp;": {"codepoints": [8202], "characters": " "}, "&half;": {"codepoints": [189], "characters": "½"}, "&hamilt;": {"codepoints": [8459], "characters": "ℋ"}, "&hardcy;": {"codepoints": [1098], "characters": "ъ"}, "&harr;": {"codepoints": [8596], "characters": "↔"}, "&harrcir;": {"codepoints": [10568], "characters": "⥈"}, "&harrw;": {"codepoints": [8621], "characters": "↭"}, "&hbar;": {"codepoints": [8463], "characters": "ℏ"}, "&hcirc;": {"codepoints": [293], "characters": "ĥ"}, "&hearts;": {"codepoints": [9829], "characters": "♥"}, "&heartsuit;": {"codepoints": [9829], "characters": "♥"}, "&hellip;": {"codepoints": [8230], "characters": "…"}, "&hercon;": {"codepoints": [8889], "characters": "⊹"}, "&hfr;": {"codepoints": [120101], "characters": "𝔥"}, "&hksearow;": {"codepoints": [10533], "characters": "⤥"}, "&hkswarow;": {"codepoints": [10534], "characters": "⤦"}, "&hoarr;": {"codepoints": [8703], "characters": "⇿"}, "&homtht;": {"codepoints": [8763], "characters": "∻"}, "&hookleftarrow;": {"codepoints": [8617], "characters": "↩"}, "&hookrightarrow;": {"codepoints": [8618], "characters": "↪"}, "&hopf;": {"codepoints": [120153], "characters": "𝕙"}, "&horbar;": {"codepoints": [8213], "characters": "―"}, "&hscr;": {"codepoints": [119997], "characters": "𝒽"}, "&hslash;": {"codepoints": [8463], "characters": "ℏ"}, "&hstrok;": {"codepoints": [295], "characters": "ħ"}, "&hybull;": {"codepoints": [8259], "characters": "⁃"}, "&hyphen;": {"codepoints": [8208], "characters": "‐"}, "&iacute": {"codepoints": [237], "characters": "í"}, "&iacute;": {"codepoints": [237], "characters": "í"}, "&ic;": {"codepoints": [8291], "characters": "⁣"}, "&icirc": {"codepoints": [238], "characters": "î"}, "&icirc;": {"codepoints": [238], "characters": "î"}, "&icy;": {"codepoints": [1080], "characters": "и"}, "&iecy;": {"codepoints": [1077], "characters": "е"}, "&iexcl": {"codepoints": [161], "characters": "¡"}, "&iexcl;": {"codepoints": [161], "characters": "¡"}, "&iff;": {"codepoints": [8660], "characters": "⇔"}, "&ifr;": {"codepoints": [120102], "characters": "𝔦"}, "&igrave": {"codepoints": [236], "characters": "ì"}, "&igrave;": {"codepoints": [236], "characters": "ì"}, "&ii;": {"codepoints": [8520], "characters": "ⅈ"}, "&iiiint;": {"codepoints": [10764], "characters": "⨌"}, "&iiint;": {"codepoints": [8749], "characters": "∭"}, "&iinfin;": {"codepoints": [10716], "characters": "⧜"}, "&iiota;": {"codepoints": [8489], "characters": "℩"}, "&ijlig;": {"codepoints": [307], "characters": "ĳ"}, "&imacr;": {"codepoints": [299], "characters": "ī"}, "&image;": {"codepoints": [8465], "characters": "ℑ"}, "&imagline;": {"codepoints": [8464], "characters": "ℐ"}, "&imagpart;": {"codepoints": [8465], "characters": "ℑ"}, "&imath;": {"codepoints": [305], "characters": "ı"}, "&imof;": {"codepoints": [8887], "characters": "⊷"}, "&imped;": {"codepoints": [437], "characters": "Ƶ"}, "&in;": {"codepoints": [8712], "characters": "∈"}, "&incare;": {"codepoints": [8453], "characters": "℅"}, "&infin;": {"codepoints": [8734], "characters": "∞"}, "&infintie;": {"codepoints": [10717], "characters": "⧝"}, "&inodot;": {"codepoints": [305], "characters": "ı"}, "&int;": {"codepoints": [8747], "characters": "∫"}, "&intcal;": {"codepoints": [8890], "characters": "⊺"}, "&integers;": {"codepoints": [8484], "characters": "ℤ"}, "&intercal;": {"codepoints": [8890], "characters": "⊺"}, "&intlarhk;": {"codepoints": [10775], "characters": "⨗"}, "&intprod;": {"codepoints": [10812], "characters": "⨼"}, "&iocy;": {"codepoints": [1105], "characters": "ё"}, "&iogon;": {"codepoints": [303], "characters": "į"}, "&iopf;": {"codepoints": [120154], "characters": "𝕚"}, "&iota;": {"codepoints": [953], "characters": "ι"}, "&iprod;": {"codepoints": [10812], "characters": "⨼"}, "&iquest": {"codepoints": [191], "characters": "¿"}, "&iquest;": {"codepoints": [191], "characters": "¿"}, "&iscr;": {"codepoints": [119998], "characters": "𝒾"}, "&isin;": {"codepoints": [8712], "characters": "∈"}, "&isinE;": {"codepoints": [8953], "characters": "⋹"}, "&isindot;": {"codepoints": [8949], "characters": "⋵"}, "&isins;": {"codepoints": [8948], "characters": "⋴"}, "&isinsv;": {"codepoints": [8947], "characters": "⋳"}, "&isinv;": {"codepoints": [8712], "characters": "∈"}, "&it;": {"codepoints": [8290], "characters": "⁢"}, "&itilde;": {"codepoints": [297], "characters": "ĩ"}, "&iukcy;": {"codepoints": [1110], "characters": "і"}, "&iuml": {"codepoints": [239], "characters": "ï"}, "&iuml;": {"codepoints": [239], "characters": "ï"}, "&jcirc;": {"codepoints": [309], "characters": "ĵ"}, "&jcy;": {"codepoints": [1081], "characters": "й"}, "&jfr;": {"codepoints": [120103], "characters": "𝔧"}, "&jmath;": {"codepoints": [567], "characters": "ȷ"}, "&jopf;": {"codepoints": [120155], "characters": "𝕛"}, "&jscr;": {"codepoints": [119999], "characters": "𝒿"}, "&jsercy;": {"codepoints": [1112], "characters": "ј"}, "&jukcy;": {"codepoints": [1108], "characters": "є"}, "&kappa;": {"codepoints": [954], "characters": "κ"}, "&kappav;": {"codepoints": [1008], "characters": "ϰ"}, "&kcedil;": {"codepoints": [311], "characters": "ķ"}, "&kcy;": {"codepoints": [1082], "characters": "к"}, "&kfr;": {"codepoints": [120104], "characters": "𝔨"}, "&kgreen;": {"codepoints": [312], "characters": "ĸ"}, "&khcy;": {"codepoints": [1093], "characters": "х"}, "&kjcy;": {"codepoints": [1116], "characters": "ќ"}, "&kopf;": {"codepoints": [120156], "characters": "𝕜"}, "&kscr;": {"codepoints": [120000], "characters": "𝓀"}, "&lAarr;": {"codepoints": [8666], "characters": "⇚"}, "&lArr;": {"codepoints": [8656], "characters": "⇐"}, "&lAtail;": {"codepoints": [10523], "characters": "⤛"}, "&lBarr;": {"codepoints": [10510], "characters": "⤎"}, "&lE;": {"codepoints": [8806], "characters": "≦"}, "&lEg;": {"codepoints": [10891], "characters": "⪋"}, "&lHar;": {"codepoints": [10594], "characters": "⥢"}, "&lacute;": {"codepoints": [314], "characters": "ĺ"}, "&laemptyv;": {"codepoints": [10676], "characters": "⦴"}, "&lagran;": {"codepoints": [8466], "characters": "ℒ"}, "&lambda;": {"codepoints": [955], "characters": "λ"}, "&lang;": {"codepoints": [10216], "characters": "⟨"}, "&langd;": {"codepoints": [10641], "characters": "⦑"}, "&langle;": {"codepoints": [10216], "characters": "⟨"}, "&lap;": {"codepoints": [10885], "characters": "⪅"}, "&laquo": {"codepoints": [171], "characters": "«"}, "&laquo;": {"codepoints": [171], "characters": "«"}, "&larr;": {"codepoints": [8592], "characters": "←"}, "&larrb;": {"codepoints": [8676], "characters": "⇤"}, "&larrbfs;": {"codepoints": [10527], "characters": "⤟"}, "&larrfs;": {"codepoints": [10525], "characters": "⤝"}, "&larrhk;": {"codepoints": [8617], "characters": "↩"}, "&larrlp;": {"codepoints": [8619], "characters": "↫"}, "&larrpl;": {"codepoints": [10553], "characters": "⤹"}, "&larrsim;": {"codepoints": [10611], "characters": "⥳"}, "&larrtl;": {"codepoints": [8610], "characters": "↢"}, "&lat;": {"codepoints": [10923], "characters": "⪫"}, "&latail;": {"codepoints": [10521], "characters": "⤙"}, "&late;": {"codepoints": [10925], "characters": "⪭"}, "&lates;": {"codepoints": [10925, 65024], "characters": "⪭︀"}, "&lbarr;": {"codepoints": [10508], "characters": "⤌"}, "&lbbrk;": {"codepoints": [10098], "characters": "❲"}, "&lbrace;": {"codepoints": [123], "characters": "{"}, "&lbrack;": {"codepoints": [91], "characters": "["}, "&lbrke;": {"codepoints": [10635], "characters": "⦋"}, "&lbrksld;": {"codepoints": [10639], "characters": "⦏"}, "&lbrkslu;": {"codepoints": [10637], "characters": "⦍"}, "&lcaron;": {"codepoints": [318], "characters": "ľ"}, "&lcedil;": {"codepoints": [316], "characters": "ļ"}, "&lceil;": {"codepoints": [8968], "characters": "⌈"}, "&lcub;": {"codepoints": [123], "characters": "{"}, "&lcy;": {"codepoints": [1083], "characters": "л"}, "&ldca;": {"codepoints": [10550], "characters": "⤶"}, "&ldquo;": {"codepoints": [8220], "characters": "“"}, "&ldquor;": {"codepoints": [8222], "characters": "„"}, "&ldrdhar;": {"codepoints": [10599], "characters": "⥧"}, "&ldrushar;": {"codepoints": [10571], "characters": "⥋"}, "&ldsh;": {"codepoints": [8626], "characters": "↲"}, "&le;": {"codepoints": [8804], "characters": "≤"}, "&leftarrow;": {"codepoints": [8592], "characters": "←"}, "&leftarrowtail;": {"codepoints": [8610], "characters": "↢"}, "&leftharpoondown;": {"codepoints": [8637], "characters": "↽"}, "&leftharpoonup;": {"codepoints": [8636], "characters": "↼"}, "&leftleftarrows;": {"codepoints": [8647], "characters": "⇇"}, "&leftrightarrow;": {"codepoints": [8596], "characters": "↔"}, "&leftrightarrows;": {"codepoints": [8646], "characters": "⇆"}, "&leftrightharpoons;": {"codepoints": [8651], "characters": "⇋"}, "&leftrightsquigarrow;": {"codepoints": [8621], "characters": "↭"}, "&leftthreetimes;": {"codepoints": [8907], "characters": "⋋"}, "&leg;": {"codepoints": [8922], "characters": "⋚"}, "&leq;": {"codepoints": [8804], "characters": "≤"}, "&leqq;": {"codepoints": [8806], "characters": "≦"}, "&leqslant;": {"codepoints": [10877], "characters": "⩽"}, "&les;": {"codepoints": [10877], "characters": "⩽"}, "&lescc;": {"codepoints": [10920], "characters": "⪨"}, "&lesdot;": {"codepoints": [10879], "characters": "⩿"}, "&lesdoto;": {"codepoints": [10881], "characters": "⪁"}, "&lesdotor;": {"codepoints": [10883], "characters": "⪃"}, "&lesg;": {"codepoints": [8922, 65024], "characters": "⋚︀"}, "&lesges;": {"codepoints": [10899], "characters": "⪓"}, "&lessapprox;": {"codepoints": [10885], "characters": "⪅"}, "&lessdot;": {"codepoints": [8918], "characters": "⋖"}, "&lesseqgtr;": {"codepoints": [8922], "characters": "⋚"}, "&lesseqqgtr;": {"codepoints": [10891], "characters": "⪋"}, "&lessgtr;": {"codepoints": [8822], "characters": "≶"}, "&lesssim;": {"codepoints": [8818], "characters": "≲"}, "&lfisht;": {"codepoints": [10620], "characters": "⥼"}, "&lfloor;": {"codepoints": [8970], "characters": "⌊"}, "&lfr;": {"codepoints": [120105], "characters": "𝔩"}, "&lg;": {"codepoints": [8822], "characters": "≶"}, "&lgE;": {"codepoints": [10897], "characters": "⪑"}, "&lhard;": {"codepoints": [8637], "characters": "↽"}, "&lharu;": {"codepoints": [8636], "characters": "↼"}, "&lharul;": {"codepoints": [10602], "characters": "⥪"}, "&lhblk;": {"codepoints": [9604], "characters": "▄"}, "&ljcy;": {"codepoints": [1113], "characters": "љ"}, "&ll;": {"codepoints": [8810], "characters": "≪"}, "&llarr;": {"codepoints": [8647], "characters": "⇇"}, "&llcorner;": {"codepoints": [8990], "characters": "⌞"}, "&llhard;": {"codepoints": [10603], "characters": "⥫"}, "&lltri;": {"codepoints": [9722], "characters": "◺"}, "&lmidot;": {"codepoints": [320], "characters": "ŀ"}, "&lmoust;": {"codepoints": [9136], "characters": "⎰"}, "&lmoustache;": {"codepoints": [9136], "characters": "⎰"}, "&lnE;": {"codepoints": [8808], "characters": "≨"}, "&lnap;": {"codepoints": [10889], "characters": "⪉"}, "&lnapprox;": {"codepoints": [10889], "characters": "⪉"}, "&lne;": {"codepoints": [10887], "characters": "⪇"}, "&lneq;": {"codepoints": [10887], "characters": "⪇"}, "&lneqq;": {"codepoints": [8808], "characters": "≨"}, "&lnsim;": {"codepoints": [8934], "characters": "⋦"}, "&loang;": {"codepoints": [10220], "characters": "⟬"}, "&loarr;": {"codepoints": [8701], "characters": "⇽"}, "&lobrk;": {"codepoints": [10214], "characters": "⟦"}, "&longleftarrow;": {"codepoints": [10229], "characters": "⟵"}, "&longleftrightarrow;": {"codepoints": [10231], "characters": "⟷"}, "&longmapsto;": {"codepoints": [10236], "characters": "⟼"}, "&longrightarrow;": {"codepoints": [10230], "characters": "⟶"}, "&looparrowleft;": {"codepoints": [8619], "characters": "↫"}, "&looparrowright;": {"codepoints": [8620], "characters": "↬"}, "&lopar;": {"codepoints": [10629], "characters": "⦅"}, "&lopf;": {"codepoints": [120157], "characters": "𝕝"}, "&loplus;": {"codepoints": [10797], "characters": "⨭"}, "&lotimes;": {"codepoints": [10804], "characters": "⨴"}, "&lowast;": {"codepoints": [8727], "characters": "∗"}, "&lowbar;": {"codepoints": [95], "characters": "_"}, "&loz;": {"codepoints": [9674], "characters": "◊"}, "&lozenge;": {"codepoints": [9674], "characters": "◊"}, "&lozf;": {"codepoints": [10731], "characters": "⧫"}, "&lpar;": {"codepoints": [40], "characters": "("}, "&lparlt;": {"codepoints": [10643], "characters": "⦓"}, "&lrarr;": {"codepoints": [8646], "characters": "⇆"}, "&lrcorner;": {"codepoints": [8991], "characters": "⌟"}, "&lrhar;": {"codepoints": [8651], "characters": "⇋"}, "&lrhard;": {"codepoints": [10605], "characters": "⥭"}, "&lrm;": {"codepoints": [8206], "characters": "‎"}, "&lrtri;": {"codepoints": [8895], "characters": "⊿"}, "&lsaquo;": {"codepoints": [8249], "characters": "‹"}, "&lscr;": {"codepoints": [120001], "characters": "𝓁"}, "&lsh;": {"codepoints": [8624], "characters": "↰"}, "&lsim;": {"codepoints": [8818], "characters": "≲"}, "&lsime;": {"codepoints": [10893], "characters": "⪍"}, "&lsimg;": {"codepoints": [10895], "characters": "⪏"}, "&lsqb;": {"codepoints": [91], "characters": "["}, "&lsquo;": {"codepoints": [8216], "characters": "‘"}, "&lsquor;": {"codepoints": [8218], "characters": "‚"}, "&lstrok;": {"codepoints": [322], "characters": "ł"}, "&lt": {"codepoints": [60], "characters": "<"}, "&lt;": {"codepoints": [60], "characters": "<"}, "&ltcc;": {"codepoints": [10918], "characters": "⪦"}, "&ltcir;": {"codepoints": [10873], "characters": "⩹"}, "&ltdot;": {"codepoints": [8918], "characters": "⋖"}, "&lthree;": {"codepoints": [8907], "characters": "⋋"}, "&ltimes;": {"codepoints": [8905], "characters": "⋉"}, "&ltlarr;": {"codepoints": [10614], "characters": "⥶"}, "&ltquest;": {"codepoints": [10875], "characters": "⩻"}, "&ltrPar;": {"codepoints": [10646], "characters": "⦖"}, "&ltri;": {"codepoints": [9667], "characters": "◃"}, "&ltrie;": {"codepoints": [8884], "characters": "⊴"}, "&ltrif;": {"codepoints": [9666], "characters": "◂"}, "&lurdshar;": {"codepoints": [10570], "characters": "⥊"}, "&luruhar;": {"codepoints": [10598], "characters": "⥦"}, "&lvertneqq;": {"codepoints": [8808, 65024], "characters": "≨︀"}, "&lvnE;": {"codepoints": [8808, 65024], "characters": "≨︀"}, "&mDDot;": {"codepoints": [8762], "characters": "∺"}, "&macr": {"codepoints": [175], "characters": "¯"}, "&macr;": {"codepoints": [175], "characters": "¯"}, "&male;": {"codepoints": [9794], "characters": "♂"}, "&malt;": {"codepoints": [10016], "characters": "✠"}, "&maltese;": {"codepoints": [10016], "characters": "✠"}, "&map;": {"codepoints": [8614], "characters": "↦"}, "&mapsto;": {"codepoints": [8614], "characters": "↦"}, "&mapstodown;": {"codepoints": [8615], "characters": "↧"}, "&mapstoleft;": {"codepoints": [8612], "characters": "↤"}, "&mapstoup;": {"codepoints": [8613], "characters": "↥"}, "&marker;": {"codepoints": [9646], "characters": "▮"}, "&mcomma;": {"codepoints": [10793], "characters": "⨩"}, "&mcy;": {"codepoints": [1084], "characters": "м"}, "&mdash;": {"codepoints": [8212], "characters": "—"}, "&measuredangle;": {"codepoints": [8737], "characters": "∡"}, "&mfr;": {"codepoints": [120106], "characters": "𝔪"}, "&mho;": {"codepoints": [8487], "characters": "℧"}, "&micro": {"codepoints": [181], "characters": "µ"}, "&micro;": {"codepoints": [181], "characters": "µ"}, "&mid;": {"codepoints": [8739], "characters": "∣"}, "&midast;": {"codepoints": [42], "characters": "*"}, "&midcir;": {"codepoints": [10992], "characters": "⫰"}, "&middot": {"codepoints": [183], "characters": "·"}, "&middot;": {"codepoints": [183], "characters": "·"}, "&minus;": {"codepoints": [8722], "characters": "−"}, "&minusb;": {"codepoints": [8863], "characters": "⊟"}, "&minusd;": {"codepoints": [8760], "characters": "∸"}, "&minusdu;": {"codepoints": [10794], "characters": "⨪"}, "&mlcp;": {"codepoints": [10971], "characters": "⫛"}, "&mldr;": {"codepoints": [8230], "characters": "…"}, "&mnplus;": {"codepoints": [8723], "characters": "∓"}, "&models;": {"codepoints": [8871], "characters": "⊧"}, "&mopf;": {"codepoints": [120158], "characters": "𝕞"}, "&mp;": {"codepoints": [8723], "characters": "∓"}, "&mscr;": {"codepoints": [120002], "characters": "𝓂"}, "&mstpos;": {"codepoints": [8766], "characters": "∾"}, "&mu;": {"codepoints": [956], "characters": "μ"}, "&multimap;": {"codepoints": [8888], "characters": "⊸"}, "&mumap;": {"codepoints": [8888], "characters": "⊸"}, "&nGg;": {"codepoints": [8921, 824], "characters": "⋙̸"}, "&nGt;": {"codepoints": [8811, 8402], "characters": "≫⃒"}, "&nGtv;": {"codepoints": [8811, 824], "characters": "≫̸"}, "&nLeftarrow;": {"codepoints": [8653], "characters": "⇍"}, "&nLeftrightarrow;": {"codepoints": [8654], "characters": "⇎"}, "&nLl;": {"codepoints": [8920, 824], "characters": "⋘̸"}, "&nLt;": {"codepoints": [8810, 8402], "characters": "≪⃒"}, "&nLtv;": {"codepoints": [8810, 824], "characters": "≪̸"}, "&nRightarrow;": {"codepoints": [8655], "characters": "⇏"}, "&nVDash;": {"codepoints": [8879], "characters": "⊯"}, "&nVdash;": {"codepoints": [8878], "characters": "⊮"}, "&nabla;": {"codepoints": [8711], "characters": "∇"}, "&nacute;": {"codepoints": [324], "characters": "ń"}, "&nang;": {"codepoints": [8736, 8402], "characters": "∠⃒"}, "&nap;": {"codepoints": [8777], "characters": "≉"}, "&napE;": {"codepoints": [10864, 824], "characters": "⩰̸"}, "&napid;": {"codepoints": [8779, 824], "characters": "≋̸"}, "&napos;": {"codepoints": [329], "characters": "ŉ"}, "&napprox;": {"codepoints": [8777], "characters": "≉"}, "&natur;": {"codepoints": [9838], "characters": "♮"}, "&natural;": {"codepoints": [9838], "characters": "♮"}, "&naturals;": {"codepoints": [8469], "characters": "ℕ"}, "&nbsp": {"codepoints": [160], "characters": " "}, "&nbsp;": {"codepoints": [160], "characters": " "}, "&nbump;": {"codepoints": [8782, 824], "characters": "≎̸"}, "&nbumpe;": {"codepoints": [8783, 824], "characters": "≏̸"}, "&ncap;": {"codepoints": [10819], "characters": "⩃"}, "&ncaron;": {"codepoints": [328], "characters": "ň"}, "&ncedil;": {"codepoints": [326], "characters": "ņ"}, "&ncong;": {"codepoints": [8775], "characters": "≇"}, "&ncongdot;": {"codepoints": [10861, 824], "characters": "⩭̸"}, "&ncup;": {"codepoints": [10818], "characters": "⩂"}, "&ncy;": {"codepoints": [1085], "characters": "н"}, "&ndash;": {"codepoints": [8211], "characters": "–"}, "&ne;": {"codepoints": [8800], "characters": "≠"}, "&neArr;": {"codepoints": [8663], "characters": "⇗"}, "&nearhk;": {"codepoints": [10532], "characters": "⤤"}, "&nearr;": {"codepoints": [8599], "characters": "↗"}, "&nearrow;": {"codepoints": [8599], "characters": "↗"}, "&nedot;": {"codepoints": [8784, 824], "characters": "≐̸"}, "&nequiv;": {"codepoints": [8802], "characters": "≢"}, "&nesear;": {"codepoints": [10536], "characters": "⤨"}, "&nesim;": {"codepoints": [8770, 824], "characters": "≂̸"}, "&nexist;": {"codepoints": [8708], "characters": "∄"}, "&nexists;": {"codepoints": [8708], "characters": "∄"}, "&nfr;": {"codepoints": [120107], "characters": "𝔫"}, "&ngE;": {"codepoints": [8807, 824], "characters": "≧̸"}, "&nge;": {"codepoints": [8817], "characters": "≱"}, "&ngeq;": {"codepoints": [8817], "characters": "≱"}, "&ngeqq;": {"codepoints": [8807, 824], "characters": "≧̸"}, "&ngeqslant;": {"codepoints": [10878, 824], "characters": "⩾̸"}, "&nges;": {"codepoints": [10878, 824], "characters": "⩾̸"}, "&ngsim;": {"codepoints": [8821], "characters": "≵"}, "&ngt;": {"codepoints": [8815], "characters": "≯"}, "&ngtr;": {"codepoints": [8815], "characters": "≯"}, "&nhArr;": {"codepoints": [8654], "characters": "⇎"}, "&nharr;": {"codepoints": [8622], "characters": "↮"}, "&nhpar;": {"codepoints": [10994], "characters": "⫲"}, "&ni;": {"codepoints": [8715], "characters": "∋"}, "&nis;": {"codepoints": [8956], "characters": "⋼"}, "&nisd;": {"codepoints": [8954], "characters": "⋺"}, "&niv;": {"codepoints": [8715], "characters": "∋"}, "&njcy;": {"codepoints": [1114], "characters": "њ"}, "&nlArr;": {"codepoints": [8653], "characters": "⇍"}, "&nlE;": {"codepoints": [8806, 824], "characters": "≦̸"}, "&nlarr;": {"codepoints": [8602], "characters": "↚"}, "&nldr;": {"codepoints": [8229], "characters": "‥"}, "&nle;": {"codepoints": [8816], "characters": "≰"}, "&nleftarrow;": {"codepoints": [8602], "characters": "↚"}, "&nleftrightarrow;": {"codepoints": [8622], "characters": "↮"}, "&nleq;": {"codepoints": [8816], "characters": "≰"}, "&nleqq;": {"codepoints": [8806, 824], "characters": "≦̸"}, "&nleqslant;": {"codepoints": [10877, 824], "characters": "⩽̸"}, "&nles;": {"codepoints": [10877, 824], "characters": "⩽̸"}, "&nless;": {"codepoints": [8814], "characters": "≮"}, "&nlsim;": {"codepoints": [8820], "characters": "≴"}, "&nlt;": {"codepoints": [8814], "characters": "≮"}, "&nltri;": {"codepoints": [8938], "characters": "⋪"}, "&nltrie;": {"codepoints": [8940], "characters": "⋬"}, "&nmid;": {"codepoints": [8740], "characters": "∤"}, "&nopf;": {"codepoints": [120159], "characters": "𝕟"}, "&not": {"codepoints": [172], "characters": "¬"}, "&not;": {"codepoints": [172], "characters": "¬"}, "&notin;": {"codepoints": [8713], "characters": "∉"}, "&notinE;": {"codepoints": [8953, 824], "characters": "⋹̸"}, "&notindot;": {"codepoints": [8949, 824], "characters": "⋵̸"}, "&notinva;": {"codepoints": [8713], "characters": "∉"}, "&notinvb;": {"codepoints": [8951], "characters": "⋷"}, "&notinvc;": {"codepoints": [8950], "characters": "⋶"}, "&notni;": {"codepoints": [8716], "characters": "∌"}, "&notniva;": {"codepoints": [8716], "characters": "∌"}, "&notnivb;": {"codepoints": [8958], "characters": "⋾"}, "&notnivc;": {"codepoints": [8957], "characters": "⋽"}, "&npar;": {"codepoints": [8742], "characters": "∦"}, "&nparallel;": {"codepoints": [8742], "characters": "∦"}, "&nparsl;": {"codepoints": [11005, 8421], "characters": "⫽⃥"}, "&npart;": {"codepoints": [8706, 824], "characters": "∂̸"}, "&npolint;": {"codepoints": [10772], "characters": "⨔"}, "&npr;": {"codepoints": [8832], "characters": "⊀"}, "&nprcue;": {"codepoints": [8928], "characters": "⋠"}, "&npre;": {"codepoints": [10927, 824], "characters": "⪯̸"}, "&nprec;": {"codepoints": [8832], "characters": "⊀"}, "&npreceq;": {"codepoints": [10927, 824], "characters": "⪯̸"}, "&nrArr;": {"codepoints": [8655], "characters": "⇏"}, "&nrarr;": {"codepoints": [8603], "characters": "↛"}, "&nrarrc;": {"codepoints": [10547, 824], "characters": "⤳̸"}, "&nrarrw;": {"codepoints": [8605, 824], "characters": "↝̸"}, "&nrightarrow;": {"codepoints": [8603], "characters": "↛"}, "&nrtri;": {"codepoints": [8939], "characters": "⋫"}, "&nrtrie;": {"codepoints": [8941], "characters": "⋭"}, "&nsc;": {"codepoints": [8833], "characters": "⊁"}, "&nsccue;": {"codepoints": [8929], "characters": "⋡"}, "&nsce;": {"codepoints": [10928, 824], "characters": "⪰̸"}, "&nscr;": {"codepoints": [120003], "characters": "𝓃"}, "&nshortmid;": {"codepoints": [8740], "characters": "∤"}, "&nshortparallel;": {"codepoints": [8742], "characters": "∦"}, "&nsim;": {"codepoints": [8769], "characters": "≁"}, "&nsime;": {"codepoints": [8772], "characters": "≄"}, "&nsimeq;": {"codepoints": [8772], "characters": "≄"}, "&nsmid;": {"codepoints": [8740], "characters": "∤"}, "&nspar;": {"codepoints": [8742], "characters": "∦"}, "&nsqsube;": {"codepoints": [8930], "characters": "⋢"}, "&nsqsupe;": {"codepoints": [8931], "characters": "⋣"}, "&nsub;": {"codepoints": [8836], "characters": "⊄"}, "&nsubE;": {"codepoints": [10949, 824], "characters": "⫅̸"}, "&nsube;": {"codepoints": [8840], "characters": "⊈"}, "&nsubset;": {"codepoints": [8834, 8402], "characters": "⊂⃒"}, "&nsubseteq;": {"codepoints": [8840], "characters": "⊈"}, "&nsubseteqq;": {"codepoints": [10949, 824], "characters": "⫅̸"}, "&nsucc;": {"codepoints": [8833], "characters": "⊁"}, "&nsucceq;": {"codepoints": [10928, 824], "characters": "⪰̸"}, "&nsup;": {"codepoints": [8837], "characters": "⊅"}, "&nsupE;": {"codepoints": [10950, 824], "characters": "⫆̸"}, "&nsupe;": {"codepoints": [8841], "characters": "⊉"}, "&nsupset;": {"codepoints": [8835, 8402], "characters": "⊃⃒"}, "&nsupseteq;": {"codepoints": [8841], "characters": "⊉"}, "&nsupseteqq;": {"codepoints": [10950, 824], "characters": "⫆̸"}, "&ntgl;": {"codepoints": [8825], "characters": "≹"}, "&ntilde": {"codepoints": [241], "characters": "ñ"}, "&ntilde;": {"codepoints": [241], "characters": "ñ"}, "&ntlg;": {"codepoints": [8824], "characters": "≸"}, "&ntriangleleft;": {"codepoints": [8938], "characters": "⋪"}, "&ntrianglelefteq;": {"codepoints": [8940], "characters": "⋬"}, "&ntriangleright;": {"codepoints": [8939], "characters": "⋫"}, "&ntrianglerighteq;": {"codepoints": [8941], "characters": "⋭"}, "&nu;": {"codepoints": [957], "characters": "ν"}, "&num;": {"codepoints": [35], "characters": "#"}, "&numero;": {"codepoints": [8470], "characters": "№"}, "&numsp;": {"codepoints": [8199], "characters": " "}, "&nvDash;": {"codepoints": [8877], "characters": "⊭"}, "&nvHarr;": {"codepoints": [10500], "characters": "⤄"}, "&nvap;": {"codepoints": [8781, 8402], "characters": "≍⃒"}, "&nvdash;": {"codepoints": [8876], "characters": "⊬"}, "&nvge;": {"codepoints": [8805, 8402], "characters": "≥⃒"}, "&nvgt;": {"codepoints": [62, 8402], "characters": ">⃒"}, "&nvinfin;": {"codepoints": [10718], "characters": "⧞"}, "&nvlArr;": {"codepoints": [10498], "characters": "⤂"}, "&nvle;": {"codepoints": [8804, 8402], "characters": "≤⃒"}, "&nvlt;": {"codepoints": [60, 8402], "characters": "<⃒"}, "&nvltrie;": {"codepoints": [8884, 8402], "characters": "⊴⃒"}, "&nvrArr;": {"codepoints": [10499], "characters": "⤃"}, "&nvrtrie;": {"codepoints": [8885, 8402], "characters": "⊵⃒"}, "&nvsim;": {"codepoints": [8764, 8402], "characters": "∼⃒"}, "&nwArr;": {"codepoints": [8662], "characters": "⇖"}, "&nwarhk;": {"codepoints": [10531], "characters": "⤣"}, "&nwarr;": {"codepoints": [8598], "characters": "↖"}, "&nwarrow;": {"codepoints": [8598], "characters": "↖"}, "&nwnear;": {"codepoints": [10535], "characters": "⤧"}, "&oS;": {"codepoints": [9416], "characters": "Ⓢ"}, "&oacute": {"codepoints": [243], "characters": "ó"}, "&oacute;": {"codepoints": [243], "characters": "ó"}, "&oast;": {"codepoints": [8859], "characters": "⊛"}, "&ocir;": {"codepoints": [8858], "characters": "⊚"}, "&ocirc": {"codepoints": [244], "characters": "ô"}, "&ocirc;": {"codepoints": [244], "characters": "ô"}, "&ocy;": {"codepoints": [1086], "characters": "о"}, "&odash;": {"codepoints": [8861], "characters": "⊝"}, "&odblac;": {"codepoints": [337], "characters": "ő"}, "&odiv;": {"codepoints": [10808], "characters": "⨸"}, "&odot;": {"codepoints": [8857], "characters": "⊙"}, "&odsold;": {"codepoints": [10684], "characters": "⦼"}, "&oelig;": {"codepoints": [339], "characters": "œ"}, "&ofcir;": {"codepoints": [10687], "characters": "⦿"}, "&ofr;": {"codepoints": [120108], "characters": "𝔬"}, "&ogon;": {"codepoints": [731], "characters": "˛"}, "&ograve": {"codepoints": [242], "characters": "ò"}, "&ograve;": {"codepoints": [242], "characters": "ò"}, "&ogt;": {"codepoints": [10689], "characters": "⧁"}, "&ohbar;": {"codepoints": [10677], "characters": "⦵"}, "&ohm;": {"codepoints": [937], "characters": "Ω"}, "&oint;": {"codepoints": [8750], "characters": "∮"}, "&olarr;": {"codepoints": [8634], "characters": "↺"}, "&olcir;": {"codepoints": [10686], "characters": "⦾"}, "&olcross;": {"codepoints": [10683], "characters": "⦻"}, "&oline;": {"codepoints": [8254], "characters": "‾"}, "&olt;": {"codepoints": [10688], "characters": "⧀"}, "&omacr;": {"codepoints": [333], "characters": "<PERSON>"}, "&omega;": {"codepoints": [969], "characters": "ω"}, "&omicron;": {"codepoints": [959], "characters": "ο"}, "&omid;": {"codepoints": [10678], "characters": "⦶"}, "&ominus;": {"codepoints": [8854], "characters": "⊖"}, "&oopf;": {"codepoints": [120160], "characters": "𝕠"}, "&opar;": {"codepoints": [10679], "characters": "⦷"}, "&operp;": {"codepoints": [10681], "characters": "⦹"}, "&oplus;": {"codepoints": [8853], "characters": "⊕"}, "&or;": {"codepoints": [8744], "characters": "∨"}, "&orarr;": {"codepoints": [8635], "characters": "↻"}, "&ord;": {"codepoints": [10845], "characters": "⩝"}, "&order;": {"codepoints": [8500], "characters": "ℴ"}, "&orderof;": {"codepoints": [8500], "characters": "ℴ"}, "&ordf": {"codepoints": [170], "characters": "ª"}, "&ordf;": {"codepoints": [170], "characters": "ª"}, "&ordm": {"codepoints": [186], "characters": "º"}, "&ordm;": {"codepoints": [186], "characters": "º"}, "&origof;": {"codepoints": [8886], "characters": "⊶"}, "&oror;": {"codepoints": [10838], "characters": "⩖"}, "&orslope;": {"codepoints": [10839], "characters": "⩗"}, "&orv;": {"codepoints": [10843], "characters": "⩛"}, "&oscr;": {"codepoints": [8500], "characters": "ℴ"}, "&oslash": {"codepoints": [248], "characters": "ø"}, "&oslash;": {"codepoints": [248], "characters": "ø"}, "&osol;": {"codepoints": [8856], "characters": "⊘"}, "&otilde": {"codepoints": [245], "characters": "õ"}, "&otilde;": {"codepoints": [245], "characters": "õ"}, "&otimes;": {"codepoints": [8855], "characters": "⊗"}, "&otimesas;": {"codepoints": [10806], "characters": "⨶"}, "&ouml": {"codepoints": [246], "characters": "ö"}, "&ouml;": {"codepoints": [246], "characters": "ö"}, "&ovbar;": {"codepoints": [9021], "characters": "⌽"}, "&par;": {"codepoints": [8741], "characters": "∥"}, "&para": {"codepoints": [182], "characters": "¶"}, "&para;": {"codepoints": [182], "characters": "¶"}, "&parallel;": {"codepoints": [8741], "characters": "∥"}, "&parsim;": {"codepoints": [10995], "characters": "⫳"}, "&parsl;": {"codepoints": [11005], "characters": "⫽"}, "&part;": {"codepoints": [8706], "characters": "∂"}, "&pcy;": {"codepoints": [1087], "characters": "п"}, "&percnt;": {"codepoints": [37], "characters": "%"}, "&period;": {"codepoints": [46], "characters": "."}, "&permil;": {"codepoints": [8240], "characters": "‰"}, "&perp;": {"codepoints": [8869], "characters": "⊥"}, "&pertenk;": {"codepoints": [8241], "characters": "‱"}, "&pfr;": {"codepoints": [120109], "characters": "𝔭"}, "&phi;": {"codepoints": [966], "characters": "φ"}, "&phiv;": {"codepoints": [981], "characters": "ϕ"}, "&phmmat;": {"codepoints": [8499], "characters": "ℳ"}, "&phone;": {"codepoints": [9742], "characters": "☎"}, "&pi;": {"codepoints": [960], "characters": "π"}, "&pitchfork;": {"codepoints": [8916], "characters": "⋔"}, "&piv;": {"codepoints": [982], "characters": "ϖ"}, "&planck;": {"codepoints": [8463], "characters": "ℏ"}, "&planckh;": {"codepoints": [8462], "characters": "ℎ"}, "&plankv;": {"codepoints": [8463], "characters": "ℏ"}, "&plus;": {"codepoints": [43], "characters": "+"}, "&plusacir;": {"codepoints": [10787], "characters": "⨣"}, "&plusb;": {"codepoints": [8862], "characters": "⊞"}, "&pluscir;": {"codepoints": [10786], "characters": "⨢"}, "&plusdo;": {"codepoints": [8724], "characters": "∔"}, "&plusdu;": {"codepoints": [10789], "characters": "⨥"}, "&pluse;": {"codepoints": [10866], "characters": "⩲"}, "&plusmn": {"codepoints": [177], "characters": "±"}, "&plusmn;": {"codepoints": [177], "characters": "±"}, "&plussim;": {"codepoints": [10790], "characters": "⨦"}, "&plustwo;": {"codepoints": [10791], "characters": "⨧"}, "&pm;": {"codepoints": [177], "characters": "±"}, "&pointint;": {"codepoints": [10773], "characters": "⨕"}, "&popf;": {"codepoints": [120161], "characters": "𝕡"}, "&pound": {"codepoints": [163], "characters": "£"}, "&pound;": {"codepoints": [163], "characters": "£"}, "&pr;": {"codepoints": [8826], "characters": "≺"}, "&prE;": {"codepoints": [10931], "characters": "⪳"}, "&prap;": {"codepoints": [10935], "characters": "⪷"}, "&prcue;": {"codepoints": [8828], "characters": "≼"}, "&pre;": {"codepoints": [10927], "characters": "⪯"}, "&prec;": {"codepoints": [8826], "characters": "≺"}, "&precapprox;": {"codepoints": [10935], "characters": "⪷"}, "&preccurlyeq;": {"codepoints": [8828], "characters": "≼"}, "&preceq;": {"codepoints": [10927], "characters": "⪯"}, "&precnapprox;": {"codepoints": [10937], "characters": "⪹"}, "&precneqq;": {"codepoints": [10933], "characters": "⪵"}, "&precnsim;": {"codepoints": [8936], "characters": "⋨"}, "&precsim;": {"codepoints": [8830], "characters": "≾"}, "&prime;": {"codepoints": [8242], "characters": "′"}, "&primes;": {"codepoints": [8473], "characters": "ℙ"}, "&prnE;": {"codepoints": [10933], "characters": "⪵"}, "&prnap;": {"codepoints": [10937], "characters": "⪹"}, "&prnsim;": {"codepoints": [8936], "characters": "⋨"}, "&prod;": {"codepoints": [8719], "characters": "∏"}, "&profalar;": {"codepoints": [9006], "characters": "⌮"}, "&profline;": {"codepoints": [8978], "characters": "⌒"}, "&profsurf;": {"codepoints": [8979], "characters": "⌓"}, "&prop;": {"codepoints": [8733], "characters": "∝"}, "&propto;": {"codepoints": [8733], "characters": "∝"}, "&prsim;": {"codepoints": [8830], "characters": "≾"}, "&prurel;": {"codepoints": [8880], "characters": "⊰"}, "&pscr;": {"codepoints": [120005], "characters": "𝓅"}, "&psi;": {"codepoints": [968], "characters": "ψ"}, "&puncsp;": {"codepoints": [8200], "characters": " "}, "&qfr;": {"codepoints": [120110], "characters": "𝔮"}, "&qint;": {"codepoints": [10764], "characters": "⨌"}, "&qopf;": {"codepoints": [120162], "characters": "𝕢"}, "&qprime;": {"codepoints": [8279], "characters": "⁗"}, "&qscr;": {"codepoints": [120006], "characters": "𝓆"}, "&quaternions;": {"codepoints": [8461], "characters": "ℍ"}, "&quatint;": {"codepoints": [10774], "characters": "⨖"}, "&quest;": {"codepoints": [63], "characters": "?"}, "&questeq;": {"codepoints": [8799], "characters": "≟"}, "&quot": {"codepoints": [34], "characters": "\""}, "&quot;": {"codepoints": [34], "characters": "\""}, "&rAarr;": {"codepoints": [8667], "characters": "⇛"}, "&rArr;": {"codepoints": [8658], "characters": "⇒"}, "&rAtail;": {"codepoints": [10524], "characters": "⤜"}, "&rBarr;": {"codepoints": [10511], "characters": "⤏"}, "&rHar;": {"codepoints": [10596], "characters": "⥤"}, "&race;": {"codepoints": [8765, 817], "characters": "∽̱"}, "&racute;": {"codepoints": [341], "characters": "ŕ"}, "&radic;": {"codepoints": [8730], "characters": "√"}, "&raemptyv;": {"codepoints": [10675], "characters": "⦳"}, "&rang;": {"codepoints": [10217], "characters": "⟩"}, "&rangd;": {"codepoints": [10642], "characters": "⦒"}, "&range;": {"codepoints": [10661], "characters": "⦥"}, "&rangle;": {"codepoints": [10217], "characters": "⟩"}, "&raquo": {"codepoints": [187], "characters": "»"}, "&raquo;": {"codepoints": [187], "characters": "»"}, "&rarr;": {"codepoints": [8594], "characters": "→"}, "&rarrap;": {"codepoints": [10613], "characters": "⥵"}, "&rarrb;": {"codepoints": [8677], "characters": "⇥"}, "&rarrbfs;": {"codepoints": [10528], "characters": "⤠"}, "&rarrc;": {"codepoints": [10547], "characters": "⤳"}, "&rarrfs;": {"codepoints": [10526], "characters": "⤞"}, "&rarrhk;": {"codepoints": [8618], "characters": "↪"}, "&rarrlp;": {"codepoints": [8620], "characters": "↬"}, "&rarrpl;": {"codepoints": [10565], "characters": "⥅"}, "&rarrsim;": {"codepoints": [10612], "characters": "⥴"}, "&rarrtl;": {"codepoints": [8611], "characters": "↣"}, "&rarrw;": {"codepoints": [8605], "characters": "↝"}, "&ratail;": {"codepoints": [10522], "characters": "⤚"}, "&ratio;": {"codepoints": [8758], "characters": "∶"}, "&rationals;": {"codepoints": [8474], "characters": "ℚ"}, "&rbarr;": {"codepoints": [10509], "characters": "⤍"}, "&rbbrk;": {"codepoints": [10099], "characters": "❳"}, "&rbrace;": {"codepoints": [125], "characters": "}"}, "&rbrack;": {"codepoints": [93], "characters": "]"}, "&rbrke;": {"codepoints": [10636], "characters": "⦌"}, "&rbrksld;": {"codepoints": [10638], "characters": "⦎"}, "&rbrkslu;": {"codepoints": [10640], "characters": "⦐"}, "&rcaron;": {"codepoints": [345], "characters": "ř"}, "&rcedil;": {"codepoints": [343], "characters": "ŗ"}, "&rceil;": {"codepoints": [8969], "characters": "⌉"}, "&rcub;": {"codepoints": [125], "characters": "}"}, "&rcy;": {"codepoints": [1088], "characters": "р"}, "&rdca;": {"codepoints": [10551], "characters": "⤷"}, "&rdldhar;": {"codepoints": [10601], "characters": "⥩"}, "&rdquo;": {"codepoints": [8221], "characters": "”"}, "&rdquor;": {"codepoints": [8221], "characters": "”"}, "&rdsh;": {"codepoints": [8627], "characters": "↳"}, "&real;": {"codepoints": [8476], "characters": "ℜ"}, "&realine;": {"codepoints": [8475], "characters": "ℛ"}, "&realpart;": {"codepoints": [8476], "characters": "ℜ"}, "&reals;": {"codepoints": [8477], "characters": "ℝ"}, "&rect;": {"codepoints": [9645], "characters": "▭"}, "&reg": {"codepoints": [174], "characters": "®"}, "&reg;": {"codepoints": [174], "characters": "®"}, "&rfisht;": {"codepoints": [10621], "characters": "⥽"}, "&rfloor;": {"codepoints": [8971], "characters": "⌋"}, "&rfr;": {"codepoints": [120111], "characters": "𝔯"}, "&rhard;": {"codepoints": [8641], "characters": "⇁"}, "&rharu;": {"codepoints": [8640], "characters": "⇀"}, "&rharul;": {"codepoints": [10604], "characters": "⥬"}, "&rho;": {"codepoints": [961], "characters": "ρ"}, "&rhov;": {"codepoints": [1009], "characters": "ϱ"}, "&rightarrow;": {"codepoints": [8594], "characters": "→"}, "&rightarrowtail;": {"codepoints": [8611], "characters": "↣"}, "&rightharpoondown;": {"codepoints": [8641], "characters": "⇁"}, "&rightharpoonup;": {"codepoints": [8640], "characters": "⇀"}, "&rightleftarrows;": {"codepoints": [8644], "characters": "⇄"}, "&rightleftharpoons;": {"codepoints": [8652], "characters": "⇌"}, "&rightrightarrows;": {"codepoints": [8649], "characters": "⇉"}, "&rightsquigarrow;": {"codepoints": [8605], "characters": "↝"}, "&rightthreetimes;": {"codepoints": [8908], "characters": "⋌"}, "&ring;": {"codepoints": [730], "characters": "˚"}, "&risingdotseq;": {"codepoints": [8787], "characters": "≓"}, "&rlarr;": {"codepoints": [8644], "characters": "⇄"}, "&rlhar;": {"codepoints": [8652], "characters": "⇌"}, "&rlm;": {"codepoints": [8207], "characters": "‏"}, "&rmoust;": {"codepoints": [9137], "characters": "⎱"}, "&rmoustache;": {"codepoints": [9137], "characters": "⎱"}, "&rnmid;": {"codepoints": [10990], "characters": "⫮"}, "&roang;": {"codepoints": [10221], "characters": "⟭"}, "&roarr;": {"codepoints": [8702], "characters": "⇾"}, "&robrk;": {"codepoints": [10215], "characters": "⟧"}, "&ropar;": {"codepoints": [10630], "characters": "⦆"}, "&ropf;": {"codepoints": [120163], "characters": "𝕣"}, "&roplus;": {"codepoints": [10798], "characters": "⨮"}, "&rotimes;": {"codepoints": [10805], "characters": "⨵"}, "&rpar;": {"codepoints": [41], "characters": ")"}, "&rpargt;": {"codepoints": [10644], "characters": "⦔"}, "&rppolint;": {"codepoints": [10770], "characters": "⨒"}, "&rrarr;": {"codepoints": [8649], "characters": "⇉"}, "&rsaquo;": {"codepoints": [8250], "characters": "›"}, "&rscr;": {"codepoints": [120007], "characters": "𝓇"}, "&rsh;": {"codepoints": [8625], "characters": "↱"}, "&rsqb;": {"codepoints": [93], "characters": "]"}, "&rsquo;": {"codepoints": [8217], "characters": "’"}, "&rsquor;": {"codepoints": [8217], "characters": "’"}, "&rthree;": {"codepoints": [8908], "characters": "⋌"}, "&rtimes;": {"codepoints": [8906], "characters": "⋊"}, "&rtri;": {"codepoints": [9657], "characters": "▹"}, "&rtrie;": {"codepoints": [8885], "characters": "⊵"}, "&rtrif;": {"codepoints": [9656], "characters": "▸"}, "&rtriltri;": {"codepoints": [10702], "characters": "⧎"}, "&ruluhar;": {"codepoints": [10600], "characters": "⥨"}, "&rx;": {"codepoints": [8478], "characters": "℞"}, "&sacute;": {"codepoints": [347], "characters": "ś"}, "&sbquo;": {"codepoints": [8218], "characters": "‚"}, "&sc;": {"codepoints": [8827], "characters": "≻"}, "&scE;": {"codepoints": [10932], "characters": "⪴"}, "&scap;": {"codepoints": [10936], "characters": "⪸"}, "&scaron;": {"codepoints": [353], "characters": "š"}, "&sccue;": {"codepoints": [8829], "characters": "≽"}, "&sce;": {"codepoints": [10928], "characters": "⪰"}, "&scedil;": {"codepoints": [351], "characters": "ş"}, "&scirc;": {"codepoints": [349], "characters": "ŝ"}, "&scnE;": {"codepoints": [10934], "characters": "⪶"}, "&scnap;": {"codepoints": [10938], "characters": "⪺"}, "&scnsim;": {"codepoints": [8937], "characters": "⋩"}, "&scpolint;": {"codepoints": [10771], "characters": "⨓"}, "&scsim;": {"codepoints": [8831], "characters": "≿"}, "&scy;": {"codepoints": [1089], "characters": "с"}, "&sdot;": {"codepoints": [8901], "characters": "⋅"}, "&sdotb;": {"codepoints": [8865], "characters": "⊡"}, "&sdote;": {"codepoints": [10854], "characters": "⩦"}, "&seArr;": {"codepoints": [8664], "characters": "⇘"}, "&searhk;": {"codepoints": [10533], "characters": "⤥"}, "&searr;": {"codepoints": [8600], "characters": "↘"}, "&searrow;": {"codepoints": [8600], "characters": "↘"}, "&sect": {"codepoints": [167], "characters": "§"}, "&sect;": {"codepoints": [167], "characters": "§"}, "&semi;": {"codepoints": [59], "characters": ";"}, "&seswar;": {"codepoints": [10537], "characters": "⤩"}, "&setminus;": {"codepoints": [8726], "characters": "∖"}, "&setmn;": {"codepoints": [8726], "characters": "∖"}, "&sext;": {"codepoints": [10038], "characters": "✶"}, "&sfr;": {"codepoints": [120112], "characters": "𝔰"}, "&sfrown;": {"codepoints": [8994], "characters": "⌢"}, "&sharp;": {"codepoints": [9839], "characters": "♯"}, "&shchcy;": {"codepoints": [1097], "characters": "щ"}, "&shcy;": {"codepoints": [1096], "characters": "ш"}, "&shortmid;": {"codepoints": [8739], "characters": "∣"}, "&shortparallel;": {"codepoints": [8741], "characters": "∥"}, "&shy": {"codepoints": [173], "characters": "­"}, "&shy;": {"codepoints": [173], "characters": "­"}, "&sigma;": {"codepoints": [963], "characters": "σ"}, "&sigmaf;": {"codepoints": [962], "characters": "ς"}, "&sigmav;": {"codepoints": [962], "characters": "ς"}, "&sim;": {"codepoints": [8764], "characters": "∼"}, "&simdot;": {"codepoints": [10858], "characters": "⩪"}, "&sime;": {"codepoints": [8771], "characters": "≃"}, "&simeq;": {"codepoints": [8771], "characters": "≃"}, "&simg;": {"codepoints": [10910], "characters": "⪞"}, "&simgE;": {"codepoints": [10912], "characters": "⪠"}, "&siml;": {"codepoints": [10909], "characters": "⪝"}, "&simlE;": {"codepoints": [10911], "characters": "⪟"}, "&simne;": {"codepoints": [8774], "characters": "≆"}, "&simplus;": {"codepoints": [10788], "characters": "⨤"}, "&simrarr;": {"codepoints": [10610], "characters": "⥲"}, "&slarr;": {"codepoints": [8592], "characters": "←"}, "&smallsetminus;": {"codepoints": [8726], "characters": "∖"}, "&smashp;": {"codepoints": [10803], "characters": "⨳"}, "&smeparsl;": {"codepoints": [10724], "characters": "⧤"}, "&smid;": {"codepoints": [8739], "characters": "∣"}, "&smile;": {"codepoints": [8995], "characters": "⌣"}, "&smt;": {"codepoints": [10922], "characters": "⪪"}, "&smte;": {"codepoints": [10924], "characters": "⪬"}, "&smtes;": {"codepoints": [10924, 65024], "characters": "⪬︀"}, "&softcy;": {"codepoints": [1100], "characters": "ь"}, "&sol;": {"codepoints": [47], "characters": "/"}, "&solb;": {"codepoints": [10692], "characters": "⧄"}, "&solbar;": {"codepoints": [9023], "characters": "⌿"}, "&sopf;": {"codepoints": [120164], "characters": "𝕤"}, "&spades;": {"codepoints": [9824], "characters": "♠"}, "&spadesuit;": {"codepoints": [9824], "characters": "♠"}, "&spar;": {"codepoints": [8741], "characters": "∥"}, "&sqcap;": {"codepoints": [8851], "characters": "⊓"}, "&sqcaps;": {"codepoints": [8851, 65024], "characters": "⊓︀"}, "&sqcup;": {"codepoints": [8852], "characters": "⊔"}, "&sqcups;": {"codepoints": [8852, 65024], "characters": "⊔︀"}, "&sqsub;": {"codepoints": [8847], "characters": "⊏"}, "&sqsube;": {"codepoints": [8849], "characters": "⊑"}, "&sqsubset;": {"codepoints": [8847], "characters": "⊏"}, "&sqsubseteq;": {"codepoints": [8849], "characters": "⊑"}, "&sqsup;": {"codepoints": [8848], "characters": "⊐"}, "&sqsupe;": {"codepoints": [8850], "characters": "⊒"}, "&sqsupset;": {"codepoints": [8848], "characters": "⊐"}, "&sqsupseteq;": {"codepoints": [8850], "characters": "⊒"}, "&squ;": {"codepoints": [9633], "characters": "□"}, "&square;": {"codepoints": [9633], "characters": "□"}, "&squarf;": {"codepoints": [9642], "characters": "▪"}, "&squf;": {"codepoints": [9642], "characters": "▪"}, "&srarr;": {"codepoints": [8594], "characters": "→"}, "&sscr;": {"codepoints": [120008], "characters": "𝓈"}, "&ssetmn;": {"codepoints": [8726], "characters": "∖"}, "&ssmile;": {"codepoints": [8995], "characters": "⌣"}, "&sstarf;": {"codepoints": [8902], "characters": "⋆"}, "&star;": {"codepoints": [9734], "characters": "☆"}, "&starf;": {"codepoints": [9733], "characters": "★"}, "&straightepsilon;": {"codepoints": [1013], "characters": "ϵ"}, "&straightphi;": {"codepoints": [981], "characters": "ϕ"}, "&strns;": {"codepoints": [175], "characters": "¯"}, "&sub;": {"codepoints": [8834], "characters": "⊂"}, "&subE;": {"codepoints": [10949], "characters": "⫅"}, "&subdot;": {"codepoints": [10941], "characters": "⪽"}, "&sube;": {"codepoints": [8838], "characters": "⊆"}, "&subedot;": {"codepoints": [10947], "characters": "⫃"}, "&submult;": {"codepoints": [10945], "characters": "⫁"}, "&subnE;": {"codepoints": [10955], "characters": "⫋"}, "&subne;": {"codepoints": [8842], "characters": "⊊"}, "&subplus;": {"codepoints": [10943], "characters": "⪿"}, "&subrarr;": {"codepoints": [10617], "characters": "⥹"}, "&subset;": {"codepoints": [8834], "characters": "⊂"}, "&subseteq;": {"codepoints": [8838], "characters": "⊆"}, "&subseteqq;": {"codepoints": [10949], "characters": "⫅"}, "&subsetneq;": {"codepoints": [8842], "characters": "⊊"}, "&subsetneqq;": {"codepoints": [10955], "characters": "⫋"}, "&subsim;": {"codepoints": [10951], "characters": "⫇"}, "&subsub;": {"codepoints": [10965], "characters": "⫕"}, "&subsup;": {"codepoints": [10963], "characters": "⫓"}, "&succ;": {"codepoints": [8827], "characters": "≻"}, "&succapprox;": {"codepoints": [10936], "characters": "⪸"}, "&succcurlyeq;": {"codepoints": [8829], "characters": "≽"}, "&succeq;": {"codepoints": [10928], "characters": "⪰"}, "&succnapprox;": {"codepoints": [10938], "characters": "⪺"}, "&succneqq;": {"codepoints": [10934], "characters": "⪶"}, "&succnsim;": {"codepoints": [8937], "characters": "⋩"}, "&succsim;": {"codepoints": [8831], "characters": "≿"}, "&sum;": {"codepoints": [8721], "characters": "∑"}, "&sung;": {"codepoints": [9834], "characters": "♪"}, "&sup1": {"codepoints": [185], "characters": "¹"}, "&sup1;": {"codepoints": [185], "characters": "¹"}, "&sup2": {"codepoints": [178], "characters": "²"}, "&sup2;": {"codepoints": [178], "characters": "²"}, "&sup3": {"codepoints": [179], "characters": "³"}, "&sup3;": {"codepoints": [179], "characters": "³"}, "&sup;": {"codepoints": [8835], "characters": "⊃"}, "&supE;": {"codepoints": [10950], "characters": "⫆"}, "&supdot;": {"codepoints": [10942], "characters": "⪾"}, "&supdsub;": {"codepoints": [10968], "characters": "⫘"}, "&supe;": {"codepoints": [8839], "characters": "⊇"}, "&supedot;": {"codepoints": [10948], "characters": "⫄"}, "&suphsol;": {"codepoints": [10185], "characters": "⟉"}, "&suphsub;": {"codepoints": [10967], "characters": "⫗"}, "&suplarr;": {"codepoints": [10619], "characters": "⥻"}, "&supmult;": {"codepoints": [10946], "characters": "⫂"}, "&supnE;": {"codepoints": [10956], "characters": "⫌"}, "&supne;": {"codepoints": [8843], "characters": "⊋"}, "&supplus;": {"codepoints": [10944], "characters": "⫀"}, "&supset;": {"codepoints": [8835], "characters": "⊃"}, "&supseteq;": {"codepoints": [8839], "characters": "⊇"}, "&supseteqq;": {"codepoints": [10950], "characters": "⫆"}, "&supsetneq;": {"codepoints": [8843], "characters": "⊋"}, "&supsetneqq;": {"codepoints": [10956], "characters": "⫌"}, "&supsim;": {"codepoints": [10952], "characters": "⫈"}, "&supsub;": {"codepoints": [10964], "characters": "⫔"}, "&supsup;": {"codepoints": [10966], "characters": "⫖"}, "&swArr;": {"codepoints": [8665], "characters": "⇙"}, "&swarhk;": {"codepoints": [10534], "characters": "⤦"}, "&swarr;": {"codepoints": [8601], "characters": "↙"}, "&swarrow;": {"codepoints": [8601], "characters": "↙"}, "&swnwar;": {"codepoints": [10538], "characters": "⤪"}, "&szlig": {"codepoints": [223], "characters": "ß"}, "&szlig;": {"codepoints": [223], "characters": "ß"}, "&target;": {"codepoints": [8982], "characters": "⌖"}, "&tau;": {"codepoints": [964], "characters": "τ"}, "&tbrk;": {"codepoints": [9140], "characters": "⎴"}, "&tcaron;": {"codepoints": [357], "characters": "ť"}, "&tcedil;": {"codepoints": [355], "characters": "ţ"}, "&tcy;": {"codepoints": [1090], "characters": "т"}, "&tdot;": {"codepoints": [8411], "characters": "⃛"}, "&telrec;": {"codepoints": [8981], "characters": "⌕"}, "&tfr;": {"codepoints": [120113], "characters": "𝔱"}, "&there4;": {"codepoints": [8756], "characters": "∴"}, "&therefore;": {"codepoints": [8756], "characters": "∴"}, "&theta;": {"codepoints": [952], "characters": "θ"}, "&thetasym;": {"codepoints": [977], "characters": "ϑ"}, "&thetav;": {"codepoints": [977], "characters": "ϑ"}, "&thickapprox;": {"codepoints": [8776], "characters": "≈"}, "&thicksim;": {"codepoints": [8764], "characters": "∼"}, "&thinsp;": {"codepoints": [8201], "characters": " "}, "&thkap;": {"codepoints": [8776], "characters": "≈"}, "&thksim;": {"codepoints": [8764], "characters": "∼"}, "&thorn": {"codepoints": [254], "characters": "þ"}, "&thorn;": {"codepoints": [254], "characters": "þ"}, "&tilde;": {"codepoints": [732], "characters": "˜"}, "&times": {"codepoints": [215], "characters": "×"}, "&times;": {"codepoints": [215], "characters": "×"}, "&timesb;": {"codepoints": [8864], "characters": "⊠"}, "&timesbar;": {"codepoints": [10801], "characters": "⨱"}, "&timesd;": {"codepoints": [10800], "characters": "⨰"}, "&tint;": {"codepoints": [8749], "characters": "∭"}, "&toea;": {"codepoints": [10536], "characters": "⤨"}, "&top;": {"codepoints": [8868], "characters": "⊤"}, "&topbot;": {"codepoints": [9014], "characters": "⌶"}, "&topcir;": {"codepoints": [10993], "characters": "⫱"}, "&topf;": {"codepoints": [120165], "characters": "𝕥"}, "&topfork;": {"codepoints": [10970], "characters": "⫚"}, "&tosa;": {"codepoints": [10537], "characters": "⤩"}, "&tprime;": {"codepoints": [8244], "characters": "‴"}, "&trade;": {"codepoints": [8482], "characters": "™"}, "&triangle;": {"codepoints": [9653], "characters": "▵"}, "&triangledown;": {"codepoints": [9663], "characters": "▿"}, "&triangleleft;": {"codepoints": [9667], "characters": "◃"}, "&trianglelefteq;": {"codepoints": [8884], "characters": "⊴"}, "&triangleq;": {"codepoints": [8796], "characters": "≜"}, "&triangleright;": {"codepoints": [9657], "characters": "▹"}, "&trianglerighteq;": {"codepoints": [8885], "characters": "⊵"}, "&tridot;": {"codepoints": [9708], "characters": "◬"}, "&trie;": {"codepoints": [8796], "characters": "≜"}, "&triminus;": {"codepoints": [10810], "characters": "⨺"}, "&triplus;": {"codepoints": [10809], "characters": "⨹"}, "&trisb;": {"codepoints": [10701], "characters": "⧍"}, "&tritime;": {"codepoints": [10811], "characters": "⨻"}, "&trpezium;": {"codepoints": [9186], "characters": "⏢"}, "&tscr;": {"codepoints": [120009], "characters": "𝓉"}, "&tscy;": {"codepoints": [1094], "characters": "ц"}, "&tshcy;": {"codepoints": [1115], "characters": "ћ"}, "&tstrok;": {"codepoints": [359], "characters": "ŧ"}, "&twixt;": {"codepoints": [8812], "characters": "≬"}, "&twoheadleftarrow;": {"codepoints": [8606], "characters": "↞"}, "&twoheadrightarrow;": {"codepoints": [8608], "characters": "↠"}, "&uArr;": {"codepoints": [8657], "characters": "⇑"}, "&uHar;": {"codepoints": [10595], "characters": "⥣"}, "&uacute": {"codepoints": [250], "characters": "ú"}, "&uacute;": {"codepoints": [250], "characters": "ú"}, "&uarr;": {"codepoints": [8593], "characters": "↑"}, "&ubrcy;": {"codepoints": [1118], "characters": "ў"}, "&ubreve;": {"codepoints": [365], "characters": "ŭ"}, "&ucirc": {"codepoints": [251], "characters": "û"}, "&ucirc;": {"codepoints": [251], "characters": "û"}, "&ucy;": {"codepoints": [1091], "characters": "у"}, "&udarr;": {"codepoints": [8645], "characters": "⇅"}, "&udblac;": {"codepoints": [369], "characters": "ű"}, "&udhar;": {"codepoints": [10606], "characters": "⥮"}, "&ufisht;": {"codepoints": [10622], "characters": "⥾"}, "&ufr;": {"codepoints": [120114], "characters": "𝔲"}, "&ugrave": {"codepoints": [249], "characters": "ù"}, "&ugrave;": {"codepoints": [249], "characters": "ù"}, "&uharl;": {"codepoints": [8639], "characters": "↿"}, "&uharr;": {"codepoints": [8638], "characters": "↾"}, "&uhblk;": {"codepoints": [9600], "characters": "▀"}, "&ulcorn;": {"codepoints": [8988], "characters": "⌜"}, "&ulcorner;": {"codepoints": [8988], "characters": "⌜"}, "&ulcrop;": {"codepoints": [8975], "characters": "⌏"}, "&ultri;": {"codepoints": [9720], "characters": "◸"}, "&umacr;": {"codepoints": [363], "characters": "ū"}, "&uml": {"codepoints": [168], "characters": "¨"}, "&uml;": {"codepoints": [168], "characters": "¨"}, "&uogon;": {"codepoints": [371], "characters": "ų"}, "&uopf;": {"codepoints": [120166], "characters": "𝕦"}, "&uparrow;": {"codepoints": [8593], "characters": "↑"}, "&updownarrow;": {"codepoints": [8597], "characters": "↕"}, "&upharpoonleft;": {"codepoints": [8639], "characters": "↿"}, "&upharpoonright;": {"codepoints": [8638], "characters": "↾"}, "&uplus;": {"codepoints": [8846], "characters": "⊎"}, "&upsi;": {"codepoints": [965], "characters": "υ"}, "&upsih;": {"codepoints": [978], "characters": "ϒ"}, "&upsilon;": {"codepoints": [965], "characters": "υ"}, "&upuparrows;": {"codepoints": [8648], "characters": "⇈"}, "&urcorn;": {"codepoints": [8989], "characters": "⌝"}, "&urcorner;": {"codepoints": [8989], "characters": "⌝"}, "&urcrop;": {"codepoints": [8974], "characters": "⌎"}, "&uring;": {"codepoints": [367], "characters": "ů"}, "&urtri;": {"codepoints": [9721], "characters": "◹"}, "&uscr;": {"codepoints": [120010], "characters": "𝓊"}, "&utdot;": {"codepoints": [8944], "characters": "⋰"}, "&utilde;": {"codepoints": [361], "characters": "ũ"}, "&utri;": {"codepoints": [9653], "characters": "▵"}, "&utrif;": {"codepoints": [9652], "characters": "▴"}, "&uuarr;": {"codepoints": [8648], "characters": "⇈"}, "&uuml": {"codepoints": [252], "characters": "ü"}, "&uuml;": {"codepoints": [252], "characters": "ü"}, "&uwangle;": {"codepoints": [10663], "characters": "⦧"}, "&vArr;": {"codepoints": [8661], "characters": "⇕"}, "&vBar;": {"codepoints": [10984], "characters": "⫨"}, "&vBarv;": {"codepoints": [10985], "characters": "⫩"}, "&vDash;": {"codepoints": [8872], "characters": "⊨"}, "&vangrt;": {"codepoints": [10652], "characters": "⦜"}, "&varepsilon;": {"codepoints": [1013], "characters": "ϵ"}, "&varkappa;": {"codepoints": [1008], "characters": "ϰ"}, "&varnothing;": {"codepoints": [8709], "characters": "∅"}, "&varphi;": {"codepoints": [981], "characters": "ϕ"}, "&varpi;": {"codepoints": [982], "characters": "ϖ"}, "&varpropto;": {"codepoints": [8733], "characters": "∝"}, "&varr;": {"codepoints": [8597], "characters": "↕"}, "&varrho;": {"codepoints": [1009], "characters": "ϱ"}, "&varsigma;": {"codepoints": [962], "characters": "ς"}, "&varsubsetneq;": {"codepoints": [8842, 65024], "characters": "⊊︀"}, "&varsubsetneqq;": {"codepoints": [10955, 65024], "characters": "⫋︀"}, "&varsupsetneq;": {"codepoints": [8843, 65024], "characters": "⊋︀"}, "&varsupsetneqq;": {"codepoints": [10956, 65024], "characters": "⫌︀"}, "&vartheta;": {"codepoints": [977], "characters": "ϑ"}, "&vartriangleleft;": {"codepoints": [8882], "characters": "⊲"}, "&vartriangleright;": {"codepoints": [8883], "characters": "⊳"}, "&vcy;": {"codepoints": [1074], "characters": "в"}, "&vdash;": {"codepoints": [8866], "characters": "⊢"}, "&vee;": {"codepoints": [8744], "characters": "∨"}, "&veebar;": {"codepoints": [8891], "characters": "⊻"}, "&veeeq;": {"codepoints": [8794], "characters": "≚"}, "&vellip;": {"codepoints": [8942], "characters": "⋮"}, "&verbar;": {"codepoints": [124], "characters": "|"}, "&vert;": {"codepoints": [124], "characters": "|"}, "&vfr;": {"codepoints": [120115], "characters": "𝔳"}, "&vltri;": {"codepoints": [8882], "characters": "⊲"}, "&vnsub;": {"codepoints": [8834, 8402], "characters": "⊂⃒"}, "&vnsup;": {"codepoints": [8835, 8402], "characters": "⊃⃒"}, "&vopf;": {"codepoints": [120167], "characters": "𝕧"}, "&vprop;": {"codepoints": [8733], "characters": "∝"}, "&vrtri;": {"codepoints": [8883], "characters": "⊳"}, "&vscr;": {"codepoints": [120011], "characters": "𝓋"}, "&vsubnE;": {"codepoints": [10955, 65024], "characters": "⫋︀"}, "&vsubne;": {"codepoints": [8842, 65024], "characters": "⊊︀"}, "&vsupnE;": {"codepoints": [10956, 65024], "characters": "⫌︀"}, "&vsupne;": {"codepoints": [8843, 65024], "characters": "⊋︀"}, "&vzigzag;": {"codepoints": [10650], "characters": "⦚"}, "&wcirc;": {"codepoints": [373], "characters": "ŵ"}, "&wedbar;": {"codepoints": [10847], "characters": "⩟"}, "&wedge;": {"codepoints": [8743], "characters": "∧"}, "&wedgeq;": {"codepoints": [8793], "characters": "≙"}, "&weierp;": {"codepoints": [8472], "characters": "℘"}, "&wfr;": {"codepoints": [120116], "characters": "𝔴"}, "&wopf;": {"codepoints": [120168], "characters": "𝕨"}, "&wp;": {"codepoints": [8472], "characters": "℘"}, "&wr;": {"codepoints": [8768], "characters": "≀"}, "&wreath;": {"codepoints": [8768], "characters": "≀"}, "&wscr;": {"codepoints": [120012], "characters": "𝓌"}, "&xcap;": {"codepoints": [8898], "characters": "⋂"}, "&xcirc;": {"codepoints": [9711], "characters": "◯"}, "&xcup;": {"codepoints": [8899], "characters": "⋃"}, "&xdtri;": {"codepoints": [9661], "characters": "▽"}, "&xfr;": {"codepoints": [120117], "characters": "𝔵"}, "&xhArr;": {"codepoints": [10234], "characters": "⟺"}, "&xharr;": {"codepoints": [10231], "characters": "⟷"}, "&xi;": {"codepoints": [958], "characters": "ξ"}, "&xlArr;": {"codepoints": [10232], "characters": "⟸"}, "&xlarr;": {"codepoints": [10229], "characters": "⟵"}, "&xmap;": {"codepoints": [10236], "characters": "⟼"}, "&xnis;": {"codepoints": [8955], "characters": "⋻"}, "&xodot;": {"codepoints": [10752], "characters": "⨀"}, "&xopf;": {"codepoints": [120169], "characters": "𝕩"}, "&xoplus;": {"codepoints": [10753], "characters": "⨁"}, "&xotime;": {"codepoints": [10754], "characters": "⨂"}, "&xrArr;": {"codepoints": [10233], "characters": "⟹"}, "&xrarr;": {"codepoints": [10230], "characters": "⟶"}, "&xscr;": {"codepoints": [120013], "characters": "𝓍"}, "&xsqcup;": {"codepoints": [10758], "characters": "⨆"}, "&xuplus;": {"codepoints": [10756], "characters": "⨄"}, "&xutri;": {"codepoints": [9651], "characters": "△"}, "&xvee;": {"codepoints": [8897], "characters": "⋁"}, "&xwedge;": {"codepoints": [8896], "characters": "⋀"}, "&yacute": {"codepoints": [253], "characters": "ý"}, "&yacute;": {"codepoints": [253], "characters": "ý"}, "&yacy;": {"codepoints": [1103], "characters": "я"}, "&ycirc;": {"codepoints": [375], "characters": "ŷ"}, "&ycy;": {"codepoints": [1099], "characters": "ы"}, "&yen": {"codepoints": [165], "characters": "¥"}, "&yen;": {"codepoints": [165], "characters": "¥"}, "&yfr;": {"codepoints": [120118], "characters": "𝔶"}, "&yicy;": {"codepoints": [1111], "characters": "ї"}, "&yopf;": {"codepoints": [120170], "characters": "𝕪"}, "&yscr;": {"codepoints": [120014], "characters": "𝓎"}, "&yucy;": {"codepoints": [1102], "characters": "ю"}, "&yuml": {"codepoints": [255], "characters": "ÿ"}, "&yuml;": {"codepoints": [255], "characters": "ÿ"}, "&zacute;": {"codepoints": [378], "characters": "ź"}, "&zcaron;": {"codepoints": [382], "characters": "ž"}, "&zcy;": {"codepoints": [1079], "characters": "з"}, "&zdot;": {"codepoints": [380], "characters": "ż"}, "&zeetrf;": {"codepoints": [8488], "characters": "ℨ"}, "&zeta;": {"codepoints": [950], "characters": "ζ"}, "&zfr;": {"codepoints": [120119], "characters": "𝔷"}, "&zhcy;": {"codepoints": [1078], "characters": "ж"}, "&zigrarr;": {"codepoints": [8669], "characters": "⇝"}, "&zopf;": {"codepoints": [120171], "characters": "𝕫"}, "&zscr;": {"codepoints": [120015], "characters": "𝓏"}, "&zwj;": {"codepoints": [8205], "characters": "‍"}, "&zwnj;": {"codepoints": [8204], "characters": "‌"}}