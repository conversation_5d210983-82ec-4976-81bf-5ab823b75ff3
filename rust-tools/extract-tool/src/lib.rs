use pyo3::prelude::*;
use std::fs::File;
use std::io::{Read, Write};
use cmarker_typst::{run, Options, HtmlTagMap, HtmlTagKind, HeadingLabels, Flags, CaseInsensitive};
use hashbrown::DefaultHashBuilder;

/// 将Markdown文件转换为Typst文件
/// 参数: input_path - 输入MD文件路径, output_path - 输出Typ文件路径
#[pyfunction]
fn convert_md_to_typ(input_path: &str, output_path: &str) -> PyResult<String> {
    // 读取Markdown文件
    let mut md_file = File::open(input_path)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(format!("无法打开输入文件: {}", e)))?;
    
    let mut markdown = String::new();
    md_file.read_to_string(&mut markdown)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(format!("读取文件失败: {}", e)))?;

    // 配置HTML标签映射
    let mut html_tags: HtmlTagMap<DefaultHashBuilder> = HtmlTagMap::default();
    // 设置默认HTML标签映射
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"p"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"div"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"span"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"h1"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"h2"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"h3"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"h4"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"h5"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"h6"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"ul"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"ol"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"li"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"strong"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"em"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"code"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"pre"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"a"[..])), HtmlTagKind::Normal);
    html_tags.insert(CaseInsensitive(CaseInsensitive(&b"img"[..])), HtmlTagKind::Void);


    // 配置转换选项
    let options = Options {
        html_tags: &html_tags,
        label_prefix: "",
        label_use_prefix: "",
        heading_labels: HeadingLabels::GitHub,
        flags: Flags::SMART_PUNCTUATION | Flags::RAW_TYPST,
        h1_level: 1,
    };

    // 执行转换
    let source_bytes = run(&markdown, options)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("转换失败: {}", e)))?;

    // 写入输出文件
    let source = String::from_utf8_lossy(&source_bytes);
    let mut file = File::create(output_path)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(format!("无法创建输出文件: {}", e)))?;
    file.write_all(source.as_bytes())
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(format!("写入文件失败: {}", e)))?;

    Ok(format!("已成功提取Typst源码到 {}", output_path))
}

/// Python模块定义
#[pymodule]
fn cmarker_python(_py: Python, m: &Bound<'_, PyModule>) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(convert_md_to_typ, m)?)?;
    Ok(())
}