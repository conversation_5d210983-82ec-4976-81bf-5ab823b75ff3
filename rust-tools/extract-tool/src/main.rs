use std::env;

fn main() {
    let args: Vec<String> = env::args().collect();
    if args.len() < 3 {
        eprintln!("用法: {} <输入.md文件> <输出.typ文件>", args[0]);
        std::process::exit(1);
    }

    // For standalone binary, we would need to implement the conversion logic here
    // For now, this is mainly used as a Python extension
    eprintln!("此工具主要作为Python扩展使用。请使用Python接口调用。");
    std::process::exit(1);
}