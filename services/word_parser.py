import re
from typing import Dict, Any, List
from docx import Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import Table, _Cell
from docx.text.paragraph import Paragraph
from services.ai_chat_service import AIChatService
import asyncio
class WordToMarkdownParser:
    """
    新版解析器：
    将 Word 文档解析为纯净的 Markdown 和结构化的数据（仅表格）。
    """

    async def parse_document(self, file_path: str, use_ai_formatting: bool = True) -> Dict[str, Any]:
        """
        解析.docx文件，分离文本和表格数据。

        Args:
            file_path: Word 文件的路径。

        Returns:
            一个字典，包含:
            - "markdown_content": 包含表格占位符的Markdown文本。
            - "tables": 结构化表格数据的列表。
        """
        doc = Document(file_path)
        markdown_parts = []
        raw_text_parts = []
        tables = []
        table_counter = 1
        ai_service = AIChatService() if use_ai_formatting else None

        # doc.element.body 包含文档的所有顶级块元素（段落和表格）
        for element in doc.element.body:
            if isinstance(element, CT_P):
                paragraph = Paragraph(element, doc)
                style_name = paragraph.style.name if paragraph.style and paragraph.style.name else ""

                if style_name.startswith('Heading'):
                    match = re.search(r'\d+$', style_name)
                    if match:
                        level = int(match.group())
                        markdown_parts.append(f"{'#' * level} {paragraph.text}")
                    else:
                        markdown_parts.append(f"### {paragraph.text}")
                elif paragraph.text.strip():
                    raw_text_parts.append(paragraph.text.strip())

            elif isinstance(element, CT_Tbl):
                # 处理段落文本整体格式化为Markdown
                if raw_text_parts:
                    full_text = "\n\n".join(raw_text_parts)
                    if use_ai_formatting and ai_service is not None:
                        formatted = await ai_service.format_text_to_markdown(full_text)
                        markdown_parts.append(formatted)
                    else:
                        markdown_parts.append(full_text)
                    raw_text_parts = []

                # 表格处理
                table_id = f"table-{table_counter}"
                table_obj = Table(element, doc)
                table_data = self._table_to_structured_data(table_obj)
                table_data["id"] = table_id
                tables.append(table_data)
                markdown_parts.append(f"\n[TABLE:{table_id}]\n")
                table_counter += 1

        # 处理结尾段落（非表格）
        if raw_text_parts:
            full_text = "\n\n".join(raw_text_parts)
            if use_ai_formatting and ai_service is not None:
                formatted = await ai_service.format_text_to_markdown(full_text)
                markdown_parts.append(formatted)
            else:
                markdown_parts.append(full_text)

        full_markdown = "\n\n".join(filter(None, markdown_parts))
        return {
            "markdown_content": full_markdown,
            "tables": tables
        }

    def _table_to_structured_data(self, table: Table) -> Dict[str, Any]:
        """
        将 docx 表格对象转换为结构化的字典。
        """
        if not table.rows:
            return {"title": "", "headers": [], "rows": []}

        headers = [cell.text.strip() for cell in table.rows[0].cells]
        rows = []
        if len(table.rows) > 1:
            for row in table.rows[1:]:
                row_data = [cell.text.strip() for cell in row.cells]
                # 确保行数据长度与表头一致
                if len(row_data) == len(headers):
                    rows.append(row_data)

        # 尝试从表格前的段落中找到标题
        title = self._find_title_for_table(table)

        return {
            "title": title,
            "headers": headers,
            "rows": rows
        }

    def _find_title_for_table(self, table: Table) -> str:
        """
        尝试通过查找表格前的段落来确定表格标题。
        通常表格标题是紧邻表格上方的那个段落，以“表X”开头。
        """
        previous_element = table._element.getprevious()
        if previous_element is not None and isinstance(previous_element, CT_P):
            paragraph = Paragraph(previous_element, table._parent)
            text = paragraph.text.strip()
            # 检查文本是否符合标题模式 (例如, "表1 ...")
            if re.match(r'^表\s*\d+', text):
                return text
        return ""
