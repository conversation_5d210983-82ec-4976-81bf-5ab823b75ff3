import asyncio
import json
from typing import Dict, List, Any, Optional
from openai import AsyncOpenAI
from config import get_settings
import os

class AIChatService:
    """AI对话服务 - 处理报告编辑中的智能对话"""
    
    def __init__(self):
        settings = get_settings()
        self.client = AsyncOpenAI(
            api_key=settings.llm.api_key,
            base_url=settings.llm.base_url
        )
        self.model_name = settings.llm.model_name
        self.conversation_history = []
        
    async def process_user_message(
        self,
        message: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        处理用户消息并返回AI响应 (简化版，无意图分析)
        """
        system_prompt = """
        你是一个专业的报告写作助手。用户想要编辑报告内容，请提供具体的内容建议。
        请保持专业性和准确性，内容要符合报告的风格和结构。
        """
        
        # 获取当前内容上下文
        current_content = context.get("current_content", "")
        
        user_prompt = f"""
        用户请求：{message}
        当前上下文: {json.dumps(context)}
        当前内容：{current_content[:1000]}...
        
        请提供具体的内容建议或修改方案。
        """
        
        try:
            llm_response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            suggestion = llm_response.choices[0].message.content
            
            response = {
                "type": "content_suggestion",
                "message": "我为您提供了内容建议",
                "suggestion": suggestion,
                "action": {
                    "type": "insert_content",
                    "content": suggestion
                }
            }
            
        except Exception as e:
            response = {
                "type": "error",
                "message": f"抱歉，处理时出现错误：{str(e)}"
            }

        # 记录对话历史
        self.conversation_history.append({
            "user_message": message,
            "ai_response": response,
            "timestamp": self._get_current_timestamp(),
            "context": context.get("template_id", "unknown")
        })
        
        return response
    
    

    async def format_text_to_markdown(self, base_text: str) -> str:
        system_prompt = (
            "You are a helpful assistant skilled at formatting raw text into clean, "
            "publishable Markdown. Handle headings, lists, code blocks, inline styling, "
            "links, and blockquotes. Do not wrap the entire output in triple backticks. "
            "Only use H1, H2, H3, bold, and italic styles."
        )

        user_prompt = base_text.strip()

        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            content = response.choices[0].message.content
            if content:
                return content
            return ""
        except Exception as e:
            print(f"AI markdown formatting failed: {e}")
            return base_text  # fallback

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        import datetime
        return datetime.datetime.now().isoformat()