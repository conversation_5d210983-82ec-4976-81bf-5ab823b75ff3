"""
统一图表服务
基于D3.js的图表处理流程，确定性地从Word文档中提取图表并生成D3.js配置
"""

import json
import re
import shutil
import os
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from .chart_xml_parser import ChartXMLParser
from .d3js_generator import D3JSGenerator
from .word_image_extractor import extract_images_from_docx
from .word_parser import WordToMarkdownParser

logger = logging.getLogger(__name__)


class ChartService:
    """统一图表服务"""
    
    def __init__(self):
        self.xml_parser = ChartXMLParser()
        self.d3js_generator = D3JSGenerator()
        self.word_parser = WordToMarkdownParser()
    
    async def process_chart_xml_d3(self, 
                                   xml_content: str,
                                   chart_title: str = "",
                                   context: str = "",
                                   color_palette: str = "professional") -> Dict[str, Any]:
        """
        处理图表XML，生成D3.js兼容配置
        
        Args:
            xml_content: XML内容字符串
            chart_title: 图表标题
            context: 上下文信息（可选）
            color_palette: 颜色方案
            
        Returns:
            包含处理结果的字典
        """
        result = {
            "success": False, "parsed_data": {}, "base_config": {},
            "final_config": {}, "errors": []
        }
        
        try:
            # 直接使用XML内容生成D3配置（确定性方法）
            d3_config = self.d3js_generator.generate_d3_config(
                xml_content, chart_title, color_palette
            )
            result["parsed_data"] = {"xml_content": xml_content}  # 保存原始XML
            result["base_config"] = d3_config
            result["final_config"] = d3_config
            result["success"] = True
            
        except Exception as e:
            error_msg = f"D3图表处理过程中出错: {e}"
            result["errors"].append(error_msg)
            logger.error(error_msg, exc_info=True)
        
        return result
    
    async def process_word_document_complete_d3(self, word_file_path: str) -> Dict[str, Any]:
        """
        完整处理Word文档 - D3.js流程
        
        Args:
            word_file_path: Word文档路径
            
        Returns:
            包含处理结果的字典
        """
        result = {
            "success": False, "markdown_content": "", "tables": [], 
            "charts": [], "errors": []
        }
        
        temp_chart_dir = Path(f"./temp/chart_extraction_{Path(word_file_path).stem}")

        try:
            print(f"🚀 [1/4] 开始处理Word文档: {word_file_path}")
            # 1. 解析Word文档，分离文本和表格（异步）
            parsed_result = await self.word_parser.parse_document(word_file_path)
            markdown_with_table_placeholders = parsed_result["markdown_content"]
            extracted_tables = parsed_result["tables"]
            result["tables"] = extracted_tables
            print(f"✅ [1/4] Word解析完成. 发现 {len(extracted_tables)} 个表格.")

            # 2. 提取图表XML（使用现有逻辑）
            temp_chart_dir.mkdir(parents=True, exist_ok=True)
            images_info = extract_images_from_docx(word_file_path, str(temp_chart_dir))
            charts_info = [info for info in images_info if info.get("type") == "chart"]
            print(f"✅ [2/4] 图表元数据提取完成. 发现 {len(charts_info)} 个图表.")

            # 3. 在Markdown中插入图表占位符
            final_markdown = markdown_with_table_placeholders
            chart_title_pattern = r"(?:图|图表)\s*\d+.*?\n"
            
            # 查找所有图表标题
            found_chart_titles = re.findall(chart_title_pattern, final_markdown)
            
            # 确保找到的标题数和图表数匹配
            if len(found_chart_titles) == len(charts_info):
                for i, chart_meta in enumerate(charts_info):
                    chart_id = chart_meta.get("chart_info", {}).get("chart_id", f"chart_{i+1}")
                    # 用占位符替换第一个匹配到的标题
                    final_markdown = final_markdown.replace(found_chart_titles[i], f"\n[CHART:{chart_id}]\n\n", 1)
            else:
                # 如果不匹配，使用一个更简单的、可能不太准确的替换逻辑
                print(f"⚠️ 警告: 找到 {len(found_chart_titles)} 个图表标题，但有 {len(charts_info)} 个图表。将使用备用替换逻辑。")
                for i, chart_meta in enumerate(charts_info):
                    chart_id = chart_meta.get("chart_info", {}).get("chart_id", f"chart_{i+1}")
                    # 替换第一个找到的 "图X" 实例
                    final_markdown = re.sub(chart_title_pattern, f"\n[CHART:{chart_id}]\n\n", final_markdown, 1)

            result["markdown_content"] = final_markdown
            print(f"✅ [3/4] 图表占位符插入完成.")

            # 4. 处理并生成每个图表的D3.js配置
            generated_chart_configs = []
            for chart_meta in charts_info:
                chart_id = chart_meta.get("chart_info", {}).get("chart_id", "unknown")
                xml_content = chart_meta.get("chart_info", {}).get("xml_data", "")
                
                if not xml_content:
                    print(f"⚠️ 警告: 图表 {chart_id} 没有XML内容，跳过处理。")
                    continue

                chart_result = await self.process_chart_xml_d3(
                    xml_content, chart_title=f"图表 {chart_id}"
                )
                
                if chart_result["success"]:
                    config_data = {
                        "id": chart_id,
                        "chartId": chart_id,
                        "title": chart_result.get("final_config", {}).get("title", f"图表 {chart_id}"),
                        "config": chart_result["final_config"]
                    }
                    generated_chart_configs.append(config_data)

            result["charts"] = generated_chart_configs
            print(f"✅ [4/4] D3.js配置生成完成. 成功 {len(generated_chart_configs)} 个.")
            
            result["success"] = True

        except Exception as e:
            error_msg = f"Word文档处理失败: {str(e)}"
            result["errors"].append(error_msg)
            print(f"❌ {error_msg}")
        finally:
            if temp_chart_dir.exists():
                shutil.rmtree(temp_chart_dir, ignore_errors=True)

        return result

    def export_config_to_file(self, config: Dict[str, Any], output_path: str) -> bool:
        """导出配置到文件"""
        try:
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False