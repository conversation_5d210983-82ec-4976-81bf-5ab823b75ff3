import os
from datetime import datetime, timedelta
from typing import Optional
import secrets

from jose import J<PERSON><PERSON>rror, jwt
from passlib.context import CryptContext
from sqlalchemy.orm import Session

from models.database import User, UserSession
from models.schemas import UserCreate
from config import get_settings

# --- 配置（统一从全局设置读取，避免多处真相不一致） ---
_settings = get_settings()
SECRET_KEY = _settings.auth.secret_key
ALGORITHM = _settings.auth.algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = _settings.auth.access_token_expire_minutes

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """
    认证服务类，封装所有认证相关逻辑
    """

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证明文密码与哈希密码是否匹配"""
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码的哈希值"""
        return pwd_context.hash(password)

    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建JWT访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """通过邮箱从数据库中获取用户"""
        return db.query(User).filter(User.email == email).first()
    
    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """通过用户名从数据库中获取用户"""
        return db.query(User).filter(User.username == username).first()

    @staticmethod
    def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
        """
        验证用户凭据。
        如果成功，返回用户对象；否则返回None。
        """
        user = AuthService.get_user_by_email(db, email=email)
        if not user:
            return None
        if not AuthService.verify_password(password, user.hashed_password):
            return None
        return user

    @staticmethod
    def create_user(db: Session, user_create: UserCreate) -> User:
        """创建新用户并存入数据库"""
        hashed_password = AuthService.get_password_hash(user_create.password)
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            full_name=user_create.full_name,
            hashed_password=hashed_password,
            role=user_create.role
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user

    # ===== Refresh Token / Session Management =====
    @staticmethod
    def _generate_session_token() -> str:
        return secrets.token_urlsafe(48)

    @staticmethod
    def create_session(
        db: Session,
        user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_delta: Optional[timedelta] = None,
    ) -> UserSession:
        """创建刷新会话，并返回会话记录（包含 session_token）"""
        if not expires_delta:
            expires_delta = timedelta(days=_settings.auth.refresh_token_expire_days)
        session = UserSession(
            user_id=user.id,
            session_token=AuthService._generate_session_token(),
            expires_at=datetime.utcnow() + expires_delta,
            is_active=True,
            ip_address=ip_address,
            user_agent=user_agent,
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        return session

    @staticmethod
    def get_active_session_by_token(db: Session, token: str) -> Optional[UserSession]:
        session = db.query(UserSession).filter(
            UserSession.session_token == token,
            UserSession.is_active == True,
        ).first()
        return session

    @staticmethod
    def rotate_session(
        db: Session, session: UserSession,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> UserSession:
        session.session_token = AuthService._generate_session_token()
        session.expires_at = datetime.utcnow() + timedelta(days=_settings.auth.refresh_token_expire_days)
        if ip_address:
            session.ip_address = ip_address
        if user_agent:
            session.user_agent = user_agent
        db.add(session)
        db.commit()
        db.refresh(session)
        return session

    @staticmethod
    def revoke_session(db: Session, session: UserSession) -> None:
        session.is_active = False
        db.add(session)
        db.commit()