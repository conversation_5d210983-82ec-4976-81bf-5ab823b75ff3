# 🔐 认证架构最佳实践指南

## 🚀 新功能概览

### 已实现的增强功能

1. **ESLint 规则检测** - 自动检测认证相关代码问题
2. **Token 自动刷新** - 过期前自动刷新，提升用户体验
3. **统一错误处理** - 标准化错误响应格式和处理逻辑
4. **代理中间件** - 统一 API 代理和错误处理

## 🛡️ 认证架构统一原则

### 1. 客户端认证模式 - 使用统一工具函数

```typescript
// ✅ 正确方式 - 使用 auth.ts 工具函数
import { getAuthHeaders, authenticatedFetch } from '@/utils/auth'

// 方式1: 获取认证头
const headers = {
  'Content-Type': 'application/json',
  ...getAuthHeaders(),
}

// 方式2: 使用带自动重试的 fetch
const response = await authenticatedFetch(url, {
  method: 'POST',
  body: JSON.stringify(data)
})

// ❌ 错误方式 - 手动处理认证
if (currentConfig.apiKey) {
  headers['Authorization'] = `Bearer ${currentConfig.apiKey}`
}
const token = localStorage.getItem('access_token') // 不安全，没有过期检查
```

### 2. Next.js API 路由代理模式 - 使用统一中间件

```typescript
// ✅ 标准模式 - 使用代理中间件
import { proxyRequest, handleCORS } from '@/utils/proxy-middleware'

export async function POST(request: NextRequest) {
  const body = await request.json()
  
  return proxyRequest(request, '/api/prose/generate', {
    method: 'POST',
    body,
    accept: 'text/event-stream',
  })
}

export function OPTIONS() {
  return handleCORS()
}
```

### 3. 错误处理 - 使用统一错误格式

```typescript
// ✅ 使用错误处理工具
import { safeFetch, useErrorHandler } from '@/utils/error-handler'

// React 组件中使用
const { handleError } = useErrorHandler()

try {
  const response = await safeFetch(url)
  const data = await response.json()
} catch (error) {
  handleError(error) // 自动处理认证错误、网络错误等
}

// 或者使用 safeFetch
const response = await safeFetch(url)
```

## 📋 开发检查清单

### 新增 API 客户端代码时

- [ ] **使用认证工具**: 从 `@/utils/auth` 导入 `getAuthHeaders()` 或 `authenticatedFetch()`
- [ ] **避免硬编码**: 不直接操作 localStorage 或硬编码 Authorization 头
- [ ] **处理错误**: 使用 `safeFetch` 或 `handleApiError` 处理错误
- [ ] **通过 ESLint**: 确保代码通过认证相关的 ESLint 规则检查

### 新增 Next.js API 路由时

- [ ] **使用代理中间件**: 使用 `proxyRequest()` 替代手动 fetch
- [ ] **统一错误处理**: 让中间件自动处理错误响应
- [ ] **CORS 处理**: 使用 `handleCORS()` 统一处理 OPTIONS 请求
- [ ] **请求追踪**: 自动包含 X-Request-ID 用于调试

### 认证相关功能开发时

- [ ] **Token 管理**: 使用 `auth.ts` 中的工具函数
- [ ] **过期检测**: 自动检测 token 过期并刷新
- [ ] **自动重试**: 认证失败时自动重试一次
- [ ] **登录跳转**: 认证失败时自动跳转登录页

## 🚫 严格禁止的反模式

### ❌ 硬编码认证信息
```typescript
// 禁止硬编码 Bearer 字符串
headers['Authorization'] = 'Bearer ' + token

// 禁止直接操作 localStorage
const token = localStorage.getItem('access_token')
localStorage.setItem('some_key', value)
```

### ❌ 重复认证逻辑
```typescript
// 不要重复实现 token 解析、过期检查等逻辑
function parseJWT(token: string) { /* ... */ } // 已在 auth.ts 中实现
function isTokenExpired(token: string) { /* ... */ } // 已在 auth.ts 中实现
```

### ❌ 忽略 SSR 兼容性
```typescript
// 不要直接访问 window 或 localStorage
if (window.localStorage) { /* ... */ } // 错误方式
const token = localStorage.getItem('token') // 错误方式
```

## 🔍 ESLint 规则说明

项目配置了以下 ESLint 规则来防止认证相关问题：

1. **no-restricted-syntax**: 检测硬编码的 Authorization 头和不当的 localStorage 访问
2. **no-restricted-globals**: 限制直接访问 localStorage，强制使用工具函数

**示例错误检测:**
```typescript
// ❌ ESLint 会报错
const token = localStorage.getItem('user_token') // 只允许 access_token 和 refresh_token
headers['Authorization'] = 'Bearer ' + token // 禁止硬编码 Bearer 字符串

// ✅ 允许的代码
const accessToken = localStorage.getItem('access_token') // 允许
const refreshToken = localStorage.getItem('refresh_token') // 允许
const headers = { ...getAuthHeaders() } // 允许
```

## 🔧 核心功能特性

### Token 管理流程
1. **获取 Token**: 登录时存储到 localStorage
2. **使用 Token**: 通过 `getAuthHeaders()` 获取，自动检查过期
3. **过期检测**: 提前 5 分钟检测 token 过期
4. **自动刷新**: 过期时自动调用刷新接口
5. **失败处理**: 刷新失败时清除 token 并跳转登录页

### 错误处理流程
1. **统一格式**: 所有错误都使用 `UnifiedErrorResponse` 格式
2. **自动分类**: 根据 HTTP 状态码自动分类错误类型
3. **用户友好**: 提供用户友好的错误消息
4. **自动重试**: 网络错误和认证错误自动重试
5. **自动跳转**: 认证失败自动跳转登录页

### API 代理特性
1. **统一认证转发**: 自动转发客户端认证头
2. **请求追踪**: 自动生成 X-Request-ID 用于调试
3. **错误统一**: 标准化错误响应格式
4. **流式支持**: 支持 SSE 流式响应
5. **CORS 处理**: 统一处理跨域请求

## 📝 代码审查要点

### 认证相关审查
- [ ] 所有 fetch 调用都使用认证工具函数
- [ ] 没有 localStorage 的直接访问（除了特定文件）
- [ ] 没有 Authorization 头的硬编码
- [ ] 通过 ESLint 规则检查

### 错误处理审查
- [ ] 所有 API 调用都有适当的错误处理
- [ ] 使用统一的错误响应格式
- [ ] 认证错误有适当的重定向逻辑
- [ ] 网络错误有重试机制

### 安全性审查
- [ ] Token 存储安全（仅 localStorage）
- [ ] Token 过期检测机制完整
- [ ] 没有敏感信息泄露
- [ ] CORS 配置正确

## 🔄 迁移指南

### 现有代码迁移
1. **替换认证头获取**:
   ```typescript
   // 旧代码
   const headers = {
     'Content-Type': 'application/json',
     ...(typeof window !== 'undefined' && localStorage.getItem('access_token')
       ? { Authorization: `Bearer ${localStorage.getItem('access_token')}` }
       : {}),
   }
   
   // 新代码
   import { getAuthHeaders } from '@/utils/auth'
   const headers = {
     'Content-Type': 'application/json',
     ...getAuthHeaders(),
   }
   ```

2. **替换 fetch 调用**:
   ```typescript
   // 旧代码
   const response = await fetch(url, options)
   if (!response.ok) { /* handle error */ }
   
   // 新代码
   import { safeFetch } from '@/utils/error-handler'
   const response = await safeFetch(url, options)
   ```

3. **更新 API 路由**:
   ```typescript
   // 旧代码
   export async function POST(request: NextRequest) {
     const authHeader = request.headers.get('authorization')
     const response = await fetch(backendUrl, { /* ... */ })
     // 手动处理错误
   }
   
   // 新代码
   export async function POST(request: NextRequest) {
     const body = await request.json()
     return proxyRequest(request, '/api/endpoint', { method: 'POST', body })
   }
   ```

## 🎯 测试验证

### 认证功能测试
- [ ] Token 过期自动刷新
- [ ] 刷新失败跳转登录
- [ ] 认证头正确包含
- [ ] ESLint 规则生效

### 错误处理测试
- [ ] 401/403 错误统一格式
- [ ] 网络错误自动重试
- [ ] 用户友好错误消息
- [ ] 请求 ID 追踪

### API 代理测试
- [ ] 认证头正确转发
- [ ] 错误响应标准化
- [ ] 流式响应支持
- [ ] CORS 配置正确