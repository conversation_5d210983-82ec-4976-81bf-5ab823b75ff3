[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "AI-REPORT"
version = "1.0.0"
description = "Modern AI REPORT service"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
keywords = ["fastapi", "ai", "report", "nlp", "sql"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Database",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "openai>=1.3.7",
    "sqlparse>=0.4.4",
    "pandas>=2.1.3",
    "mysql-connector-python>=8.2.0",
    "python-multipart>=0.0.6",
    "httpx>=0.25.2",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    # 认证相关依赖
    "sqlalchemy>=2.0.0",
    "pymysql>=1.1.0",
    "passlib[bcrypt]>=1.7.4",
    "bcrypt>=4.0.1,<4.1.0",
    "python-jose[cryptography]>=3.3.0",
    "pyjwt>=2.8.0",
    "email-validator>=2.1.0",
    "jinja2>=3.1.6",
    "qwen-agent>=0.0.26",
    "mammoth>=1.9.1",
    "python-docx>=1.1.2",
    "Pillow>=10.4.0",
    "matplotlib>=3.10.3",
    "xmltodict>=0.14.2",
    "aiohttp>=3.12.15",
    "alembic>=1.16.4",
    "redis>=6.4.0",
    "extract-cmarker-source",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]

[project.urls]
Homepage = "https://github.com/your-username/intelligent-report-generator"
Repository = "https://github.com/your-username/intelligent-report-generator"
Documentation = "https://github.com/your-username/intelligent-report-generator#readme"
Issues = "https://github.com/your-username/intelligent-report-generator/issues"

[project.scripts]
ai-report = "main:main"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=src --cov-report=term-missing --cov-report=html"
asyncio_mode = "auto"

[tool.uv]
# uv specific configuration
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]

# Package configuration
package = true

# Environment configuration
environments = [
    "sys_platform == 'win32'",
    "sys_platform == 'linux'",
    "sys_platform == 'darwin'",
]

[tool.uv.sources]
extract-cmarker-source = { path = "wheels/linux-x86_64/extract_cmarker_source-0.1.0-cp37-abi3-manylinux_2_34_x86_64.whl" }

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/README.md",
    "/pyproject.toml",
]
