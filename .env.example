# 应用配置
APP_NAME="AI REPORT API"
VERSION="1.0.0"
DEBUG=false
API_PREFIX="/api/v1"
HOST="0.0.0.0"
PORT=8000

# 日志配置
LOG_LEVEL="INFO"
LOG_FORMAT="json"

# CORS配置
CORS_ORIGINS=["*"]
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# 数据库配置
DB_HOST="localhost"
DB_PORT=3306
DB_USER="root"
DB_PASSWORD="your_password"
DB_DATABASE="travel_data"
DB_CHARSET="utf8mb4"

# Redis 配置
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""
REDIS_DB=0
REDIS_URL="redis://localhost:6379/0"

# Coze PDF 解析服务配置
COZE_PDF_UPLOAD_URL=https://api.coze.cn/v1/files/upload
COZE_PDF_WORKFLOW_URL=https://api.coze.cn/v1/workflow/stream_run
COZE_PDF_API_TOKEN="your_coze_api_key"
COZE_PDF_WORKFLOW_ID="your_workflow_id"


# LLM配置
LLM_API_KEY="your_llm_api_key"
LLM_BASE_URL="https://api.openai.com/v1"
LLM_MODEL_NAME="gpt-3.5-turbo"
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2000

# 质量评估配置
QUALITY_AUTO_RECORD=true
QUALITY_THRESHOLD=0.8
QUALITY_MAX_DAILY_RECORDS=100
QUALITY_ENABLE_FILTER=true

# 认证配置
AUTH_SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
AUTH_ACCESS_TOKEN_EXPIRE_MINUTES=30
AUTH_REFRESH_TOKEN_EXPIRE_DAYS=7
AUTH_ALGORITHM="HS256"
AUTH_MIN_PASSWORD_LENGTH=6
AUTH_REQUIRE_SPECIAL_CHARS=false
AUTH_REQUIRE_NUMBERS=false
AUTH_REQUIRE_UPPERCASE=false
