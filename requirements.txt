# AI REPORT Requirements
# 注意：推荐使用uv进行依赖管理，此文件仅作为备用

# Core dependencies
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Agent frameworks
qwen-agent>=0.0.26  # Qwen-Agent framework

# LLM and AI
openai>=1.3.7

# Database
mysql-connector-python>=8.2.0
sqlparse>=0.4.4

# Data processing
pandas>=2.1.3

# HTTP client
httpx>=0.25.2
python-multipart>=0.0.6

# Logging
structlog>=23.2.0
rich>=13.7.0

# Production server
gunicorn>=21.2.0

# Build dependencies (for uv compatibility)
hatchling>=1.18.0

# Word document parsing
mammoth>=1.6.0
python-docx>=1.1.0
Pillow>=10.0.0

passlib[bcrypt]
python-jose[cryptography]
