# Pre-compiled Wheels

This directory contains pre-compiled Python wheels for the Rust-based markdown to Typst converter.

## Installation

### Linux (x86_64)
```bash
pip install wheels/linux-x86_64/extract_cmarker_source-0.1.0-cp37-abi3-manylinux_2_34_x86_64.whl
```

### Windows (x86_64)
```bash
pip install wheels/win-x86_64/extract_cmarker_source-0.1.0-cp37-abi3-win_amd64.whl
```

## Development

To rebuild from source, see `rust-tools/extract-tool/README.md`

## Usage

```python
import cmarker_python
result = cmarker_python.convert_md_to_typ("input.md", "output.typ")
print(result)
```