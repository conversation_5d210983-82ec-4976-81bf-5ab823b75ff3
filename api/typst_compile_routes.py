"""
完整的 Typst 编译路由
整合 Markdown 转换和向量编译功能，实现完整的热重载流程
"""

import os
import tempfile
import subprocess
import time
from pathlib import Path
from typing import Optional
import asyncio
from concurrent.futures import ThreadPoolExecutor

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import Response
from pydantic import BaseModel, Field
import logging

from core.security import get_current_user
from models.database import User
from core.rate_limit import limit_requests
from services.markdown_to_typst_service import MarkdownToTypstService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/typst-compile", tags=["Typst Compilation"])

class TypstCompileRequest(BaseModel):
    """完整的 Typst 编译请求模型"""
    markdown: str = Field(..., description="Markdown 内容")
    use_conversion: bool = Field(default=True, description="是否使用 Markdown 转换")
    template_name: str = Field(default="", description="选择的模板主题名称，可选")

class TypstCompileService:
    """完整的 Typst 编译服务"""

    def __init__(self):
        # 项目根目录
        self.project_root = Path(__file__).parent.parent

        # 设置字体路径
        self.fonts_dir = self.project_root / "fonts"
        self.temp_fonts_dir = Path("/tmp/typst-fonts")
        self.font_paths = f"{self.fonts_dir}:{self.temp_fonts_dir}"

        # 设置工作目录（typst-ts-cli 要求文件在工作目录内）
        self.work_dir = self.project_root / "tmp" / "typst_work"

        # 确保目录存在
        self.fonts_dir.mkdir(parents=True, exist_ok=True)
        self.temp_fonts_dir.mkdir(parents=True, exist_ok=True)
        self.work_dir.mkdir(parents=True, exist_ok=True)

        # 初始化 Markdown 转换服务
        self.markdown_service = MarkdownToTypstService()

        # 轻量线程池，承载同步编译以免阻塞事件循环
        # 将编译任务提交到线程池，避免 WebSocket/HTTP 处理线程被阻塞
        self._executor: ThreadPoolExecutor = ThreadPoolExecutor(max_workers=2)

        # 预热线程池，确保服务完全就绪
        self._warm_up_service()

    def _warm_up_service(self):
        """预热编译服务，确保所有组件就绪"""
        try:
            logger.info("开始预热编译服务...")

            # 测试 Markdown 转换服务
            test_markdown = "# 测试\n\n这是一个测试文档。"
            self.markdown_service.convert_markdown_to_typst(test_markdown)

            # 提交一个简单任务到线程池，确保线程池激活
            future = self._executor.submit(lambda: "warmup")
            future.result(timeout=5.0)  # 5秒超时

            logger.info("编译服务预热完成")

        except Exception as e:
            logger.warning(f"编译服务预热失败，但服务仍可用: {e}")

    def compile_markdown_to_vector(self, markdown_content: str, use_conversion: bool = True, template_name: str = "") -> bytes:
        """
        完整的编译流程：Markdown -> Typst -> Vector
        
        Args:
            markdown_content: Markdown 内容
            use_conversion: 是否使用 Markdown 转换
            template_name: 模板主题名称，可选
            
        Returns:
            编译后的向量文件内容
        """
        try:
            logger.info(f"开始完整编译流程，Markdown 长度: {len(markdown_content)}")

            # 在项目工作目录内创建临时文件
            import uuid
            unique_id = str(uuid.uuid4())[:8]

            typ_file_path = self.work_dir / f"temp_{unique_id}.typ"
            output_path = self.work_dir / f"temp_{unique_id}.artifact.sir.in"

            try:
                if use_conversion:
                    # 步骤1: Markdown -> Typst
                    logger.info("步骤1: 转换 Markdown 到 Typst")
                    typst_content = self.markdown_service.convert_markdown_to_typst(markdown_content)
                else:
                    # 直接使用 markdown 内容作为 typst（用于测试）
                    typst_content = markdown_content

                # 在内存中应用模板（不依赖 username 或持久化）
                if template_name:
                    try:
                        typst_content = self.markdown_service.apply_template_to_content(template_name, typst_content)
                        logger.info(f"已应用模板: {template_name}")
                    except Exception as e:
                        logger.error(f"应用模板失败（忽略并继续）: {e}")

                # 将（可能已加模板的）Typst 内容写入临时文件
                with open(typ_file_path, 'w', encoding='utf-8') as f:
                    f.write(typst_content)
                
            # 步骤2: Typst -> Vector
                logger.info("步骤2: 编译 Typst 到向量格式")

                # 构建 typst-ts-cli 命令，使用相对路径
                # 注意：typst-ts-cli 的 vector 格式会自动生成 .artifact.sir.in 文件
                cmd = [
                    "typst-ts-cli",
                    "compile",
                    "--entry", str(typ_file_path.relative_to(self.project_root)),
                    "--format", "vector"
                ]

                # 设置环境变量
                env = os.environ.copy()
                env["TYPST_FONT_PATHS"] = self.font_paths

                logger.info(f"执行 typst-ts-cli 命令: {' '.join(cmd)}")
                logger.info(f"工作目录: {self.project_root}")
                logger.info(f"字体路径: {self.font_paths}")

                # 执行编译命令，设置工作目录为项目根目录
                result = subprocess.run(
                    cmd,
                    env=env,
                    cwd=str(self.project_root),  # 设置工作目录
                    capture_output=True,
                    text=True,
                    timeout=30  # 30秒超时
                )

                if result.returncode != 0:
                    logger.error(f"typst-ts-cli 编译失败: {result.stderr}")
                    logger.error(f"typst-ts-cli 输出: {result.stdout}")
                    raise RuntimeError(f"编译失败: {result.stderr}")

                # typst-ts-cli 会自动生成 .artifact.sir.in 文件
                # 文件名格式：原文件名.artifact.sir.in
                expected_output = typ_file_path.with_suffix('.artifact.sir.in')

                if not expected_output.exists():
                    logger.error(f"预期的向量文件不存在: {expected_output}")
                    raise RuntimeError(f"向量文件生成失败: {expected_output}")

                # 读取编译结果
                with open(expected_output, "rb") as f:
                    vector_data = f.read()

                logger.info(f"完整编译成功，输出大小: {len(vector_data)} 字节")

                # 清理生成的向量文件
                if expected_output.exists():
                    expected_output.unlink()

                return vector_data

            finally:
                # 清理临时文件
                if typ_file_path.exists():
                    typ_file_path.unlink()
                # 注意：expected_output 已经在上面清理了
                    
        except subprocess.TimeoutExpired:
            logger.error("typst-ts-cli 编译超时")
            raise RuntimeError("编译超时")
        except Exception as e:
            logger.error(f"完整编译过程中发生错误: {e}")
            raise RuntimeError(f"编译失败: {e}")
    
    async def compile_markdown_to_vector_async(self, markdown_content: str, use_conversion: bool = True, template_name: str = "") -> bytes:
        """在线程池中执行编译，避免阻塞事件循环"""
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(
            self._executor,
            lambda: self.compile_markdown_to_vector(markdown_content, use_conversion, template_name)
        )

# 创建服务实例
typst_compile_service = TypstCompileService()

@router.get("/health")
async def health_check():
    """编译服务健康检查"""
    try:
        # 检查服务是否就绪
        is_ready = (
            hasattr(typst_compile_service, '_executor') and
            typst_compile_service._executor is not None and
            not typst_compile_service._executor._shutdown
        )

        return {
            "status": "healthy" if is_ready else "initializing",
            "service_ready": is_ready,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "error",
            "service_ready": False,
            "error": str(e),
            "timestamp": time.time()
        }

@router.post("/compile-full", dependencies=[Depends(limit_requests(30, 60))])
async def compile_full(
    request_model: TypstCompileRequest,
    current_user: User = Depends(get_current_user),
):
    """
    完整的编译流程：Markdown -> Typst -> Vector
    
    实现真正的热重载：
    1. 接收前端的 Markdown 内容
    2. 转换为 Typst 格式
    3. 编译为向量格式
    4. 返回向量文件供前端渲染
    """
    try:
        # 验证请求
        if not request_model.markdown.strip():
            raise HTTPException(status_code=400, detail="Markdown 内容不能为空")
        
        user_id = getattr(current_user, 'id', None)
        logger.info(
            f"处理完整编译请求: markdown_length={len(request_model.markdown)}, "
            f"use_conversion={request_model.use_conversion}",
            extra={'user_id': user_id}
        )
        
        # 执行完整编译流程
        vector_data = typst_compile_service.compile_markdown_to_vector(
            request_model.markdown, 
            request_model.use_conversion,
            getattr(request_model, 'template_name', '')
        )
        
        # 返回向量文件
        return Response(
            content=vector_data,
            media_type="application/octet-stream",
            headers={
                "Content-Disposition": "attachment; filename=compiled.artifact.sir.in",
                "Cache-Control": "no-cache",
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"完整编译错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"编译失败: {str(e)}")

@router.options("/compile-full")
async def compile_full_options() -> Response:
    """CORS 预检由全局中间件处理，这里返回 200"""
    return Response(status_code=200)


@router.post("/test-conversion")
async def test_conversion(
    request_model: TypstCompileRequest,
    current_user: User = Depends(get_current_user),
):
    """
    测试 Markdown 转换功能（仅返回 Typst 内容，不编译向量）
    """
    try:
        if not request_model.markdown.strip():
            raise HTTPException(status_code=400, detail="Markdown 内容不能为空")
        
        logger.info(f"测试 Markdown 转换，内容长度: {len(request_model.markdown)}")
        
        # 只进行 Markdown -> Typst 转换
        typst_content = typst_compile_service.markdown_service.convert_markdown_to_typst(
            request_model.markdown
        )
        
        return {
            "success": True,
            "message": "Markdown 转换成功",
            "data": {
                "typst_content": typst_content,
                "content_length": len(typst_content)
            }
        }
        
    except Exception as e:
        logger.error(f"测试转换失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"转换失败: {str(e)}")

