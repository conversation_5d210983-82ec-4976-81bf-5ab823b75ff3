"""
Typst 编译路由
处理 Typst 文件编译为向量格式的请求
"""

import os
import tempfile
import subprocess
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import Response
from pydantic import BaseModel, Field
import logging

from core.security import get_current_user
from models.database import User
from core.rate_limit import limit_requests

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/typst", tags=["Typst Compilation"])

class TypstCompileRequest(BaseModel):
    """Typst 编译请求模型"""
    markdown: str = Field(..., description="Markdown 内容（当前阶段暂不处理）")
    typ_file: Optional[str] = Field(default="demo.typ", description="要编译的 typ 文件名")

class TypstService:
    """Typst 编译服务"""
    
    def __init__(self):
        # 设置字体路径
        self.fonts_dir = Path(__file__).parent.parent / "fonts"
        self.temp_fonts_dir = Path("/tmp/typst-fonts")
        self.font_paths = f"{self.fonts_dir}:{self.temp_fonts_dir}"
        
        # 确保字体目录存在
        self.fonts_dir.mkdir(parents=True, exist_ok=True)
        self.temp_fonts_dir.mkdir(parents=True, exist_ok=True)
        
        # Typst 文件目录
        self.typst_files_dir = Path(__file__).parent.parent / "web" / "public" / "typst"
        
    def compile_typ_to_vector(self, typ_filename: str) -> bytes:
        """
        编译 typ 文件为向量格式
        
        Args:
            typ_filename: typ 文件名
            
        Returns:
            编译后的向量文件内容
        """
        try:
            # 检查 typ 文件是否存在
            typ_file_path = self.typst_files_dir / typ_filename
            if not typ_file_path.exists():
                raise FileNotFoundError(f"Typ 文件不存在: {typ_filename}")
            
            # 创建临时输出文件
            with tempfile.NamedTemporaryFile(suffix=".artifact.sir.in", delete=False) as temp_output:
                output_path = temp_output.name
            
            try:
                # 构建 typst-ts-cli 命令
                cmd = [
                    "typst-ts-cli",
                    "compile",
                    "--entry", str(typ_file_path),
                    "--format", "vector",
                    "--output", output_path
                ]
                
                # 设置环境变量
                env = os.environ.copy()
                env["TYPST_FONT_PATHS"] = self.font_paths
                
                logger.info(f"执行 typst-ts-cli 命令: {' '.join(cmd)}")
                logger.info(f"字体路径: {self.font_paths}")
                
                # 执行编译命令
                result = subprocess.run(
                    cmd,
                    env=env,
                    capture_output=True,
                    text=True,
                    timeout=30  # 30秒超时
                )
                
                if result.returncode != 0:
                    logger.error(f"typst-ts-cli 编译失败: {result.stderr}")
                    raise RuntimeError(f"编译失败: {result.stderr}")
                
                # 读取编译结果
                with open(output_path, "rb") as f:
                    vector_data = f.read()
                
                logger.info(f"成功编译 {typ_filename}，输出大小: {len(vector_data)} 字节")
                return vector_data
                
            finally:
                # 清理临时文件
                if os.path.exists(output_path):
                    os.unlink(output_path)
                    
        except subprocess.TimeoutExpired:
            logger.error("typst-ts-cli 编译超时")
            raise RuntimeError("编译超时")
        except FileNotFoundError as e:
            logger.error(f"文件未找到: {e}")
            raise RuntimeError(f"文件未找到: {e}")
        except Exception as e:
            logger.error(f"编译过程中发生错误: {e}")
            raise RuntimeError(f"编译失败: {e}")

# 创建服务实例
typst_service = TypstService()

@router.post("/compile", dependencies=[Depends(limit_requests(30, 60))])
async def compile_typst(
    request_model: TypstCompileRequest,
    current_user: User = Depends(get_current_user),
):
    """
    编译 Typst 文件为向量格式
    
    当前阶段：
    - 接收 markdown 内容（暂不处理）
    - 编译指定的 typ 文件（默认 demo.typ）
    - 返回编译后的向量文件
    """
    try:
        # 验证请求
        if not request_model.markdown.strip():
            raise HTTPException(status_code=400, detail="Markdown 内容不能为空")
        
        user_id = getattr(current_user, 'id', None)
        logger.info(
            f"处理 Typst 编译请求: markdown_length={len(request_model.markdown)}, "
            f"typ_file={request_model.typ_file}",
            extra={'user_id': user_id}
        )
        
        # 编译 typ 文件
        typ_file = request_model.typ_file or "demo.typ"
        vector_data = typst_service.compile_typ_to_vector(typ_file)
        
        # 返回向量文件
        return Response(
            content=vector_data,
            media_type="application/octet-stream",
            headers={
                "Content-Disposition": f"attachment; filename={request_model.typ_file}.artifact.sir.in",
                "Cache-Control": "no-cache",
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Typst 编译错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"编译失败: {str(e)}")

@router.options("/compile")
async def compile_typst_options() -> Response:
    """CORS 预检由全局中间件处理，这里返回 200"""
    return Response(status_code=200)

@router.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查 typst-ts-cli 是否可用
        result = subprocess.run(
            ["typst-ts-cli", "--version"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            version = result.stdout.strip()
        else:
            version = "unknown"
        
        return {
            "status": "healthy",
            "typst_cli_version": version,
            "fonts_dir": str(typst_service.fonts_dir),
            "font_paths": typst_service.font_paths,
            "timestamp": "now"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=503, detail=f"服务不健康: {str(e)}")

@router.get("/fonts")
async def list_fonts():
    """列出可用的字体"""
    try:
        # 执行 typst fonts 命令
        env = os.environ.copy()
        env["TYPST_FONT_PATHS"] = typst_service.font_paths
        
        result = subprocess.run(
            ["typst", "fonts"],
            env=env,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            fonts_output = result.stdout
        else:
            fonts_output = f"错误: {result.stderr}"
        
        return {
            "status": "success",
            "fonts_output": fonts_output,
            "font_paths": typst_service.font_paths
        }
    except Exception as e:
        logger.error(f"列出字体失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出字体失败: {str(e)}")
