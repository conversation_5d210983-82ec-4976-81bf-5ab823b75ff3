from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import json
import logging
import os
from pathlib import Path

router = APIRouter(prefix="/api", tags=["reports"])
logger = logging.getLogger(__name__)


@router.get("/charts/{chart_id}_config.json")
async def get_chart_config(chart_id: str):
    """获取图表配置文件"""
    try:
        # 构建配置文件路径
        config_dir = Path("./output/chartjs_configs")
        config_file = config_dir / f"{chart_id}_config.json"
        
        # 检查文件是否存在
        if not config_file.exists():
            raise HTTPException(status_code=404, detail=f"图表配置文件未找到: {chart_id}")
        
        # 读取并返回JSON配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return JSONResponse(content=config_data)
        
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail=f"图表配置文件未找到: {chart_id}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail=f"图表配置文件格式错误: {chart_id}")
    except Exception as e:
        logger.error(f"获取图表配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图表配置失败: {str(e)}")

@router.get("/charts/configs")
async def list_chart_configs():
    """获取所有可用的图表配置文件列表"""
    try:
        config_dir = Path("./output/chartjs_configs")
        
        if not config_dir.exists():
            return {"success": True, "charts": []}
        
        chart_configs = []
        for config_file in config_dir.glob("chart_*_config.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                chart_id = config_file.stem.replace('_config', '')
                chart_configs.append({
                    "id": chart_id,
                    "title": config_data.get("options", {}).get("plugins", {}).get("title", {}).get("text", chart_id),
                    "type": config_data.get("type", "unknown"),
                    "configFile": f"/api/charts/{config_file.name}",
                    "lastModified": os.path.getmtime(config_file)
                })
                
            except Exception as e:
                logger.warning(f"跳过无效配置文件 {config_file}: {str(e)}")
                continue
        
        return {"success": True, "charts": chart_configs}
        
    except Exception as e:
        logger.error(f"获取图表配置列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图表配置列表失败: {str(e)}")

