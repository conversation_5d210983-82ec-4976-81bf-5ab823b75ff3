import shutil
from uuid import uuid4
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from sqlalchemy.orm import Session
from typing import List
from pathlib import Path
import traceback
import zipfile

from database.connection import get_db
from models import schemas
from models.database import ProcessedDocument
from services.chart_service import ChartService
# 暂时注释认证相关导入
# from core.security import get_current_user, require_roles
# from models.database import User
from core.rate_limit import limit_requests

router = APIRouter(
    prefix="/api/word-format",
    tags=["Word Document Processing"],
)

MAX_DOCX_BYTES = 20 * 1024 * 1024  # 20MB


@router.post("/upload", response_model=schemas.ProcessedDocumentMetadata, dependencies=[Depends(limit_requests(30, 60))])
async def upload_and_process_word_document(
    file: UploadFile = File(...), 
    db: Session = Depends(get_db),
    # 暂时注释认证
    # current_user: User = Depends(get_current_user),
):
    """
    上传、解析Word文档，生成D3.js兼容的图表配置
    """
    # 基础校验：MIME 与后缀
    if not file.filename or not file.filename.lower().endswith('.docx'):
        raise HTTPException(status_code=400, detail="仅支持 .docx 文件")
    if file.content_type not in (
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/octet-stream',  # 某些浏览器可能退化为此
    ):
        raise HTTPException(status_code=400, detail="不合法的文件类型")

    tmp_dir = Path("./tmp/word_uploads")
    tmp_dir.mkdir(parents=True, exist_ok=True)
    # 使用随机文件名，避免覆盖与路径注入
    random_name = f"{uuid4().hex}.docx"
    file_path = tmp_dir / random_name
    
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        # 大小限制
        if file_path.stat().st_size > MAX_DOCX_BYTES:
            raise HTTPException(status_code=413, detail="文件过大，最大支持 20MB")
        # 魔数与结构校验（docx 为 ZIP，且应包含 word/document.xml）
        if not zipfile.is_zipfile(file_path):
            raise HTTPException(status_code=400, detail="文件格式无效（非有效的 .docx）")
        try:
            with zipfile.ZipFile(file_path, 'r') as zf:
                if 'word/document.xml' not in zf.namelist():
                    raise HTTPException(status_code=400, detail="文件内容无效（缺少文档结构）")
        except zipfile.BadZipFile:
            raise HTTPException(status_code=400, detail="文件已损坏或非有效的 .docx")

        chart_service = ChartService()
        # 调用D3.js处理函数
        result = await chart_service.process_word_document_complete_d3(str(file_path))

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"文档处理失败: {result['errors']}")

        # 使用新的数据结构创建数据库记录
        doc_to_create = schemas.ProcessedDocumentCreate(
            filename=file.filename,
            markdown_content=result["markdown_content"],
            tables=result["tables"],
            charts=result["charts"]
        )
        
        db_document = ProcessedDocument(**doc_to_create.model_dump())
        db.add(db_document)
        db.commit()
        db.refresh(db_document)

        return db_document

    except Exception as e:
        # 在开发过程中，打印完整的异常以帮助调试
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"处理文件时发生严重错误: {str(e)}")
    finally:
        # 清理临时文件
        if file_path.exists():
            file_path.unlink()


@router.post("/upload-d3", dependencies=[Depends(limit_requests(30, 60))])
async def upload_and_process_word_document_d3(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    # current_user: User = Depends(get_current_user),
):
    """
    上传、解析Word文档，生成D3.js兼容的图表配置
    """
    if not file.filename or not file.filename.lower().endswith('.docx'):
        raise HTTPException(status_code=400, detail="仅支持 .docx 文件")
    if file.content_type not in (
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/octet-stream',
    ):
        return {
            "success": False,
            "error": "不合法的文件类型",
            "errors": ["invalid content type"],
            "charts": [],
            "tables": [],
            "markdown_content": ""
        }

    tmp_dir = Path("./tmp/word_uploads")
    tmp_dir.mkdir(parents=True, exist_ok=True)
    random_name = f"{uuid4().hex}.docx"
    file_path = tmp_dir / random_name
    
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        if file_path.stat().st_size > MAX_DOCX_BYTES:
            return {
                "success": False,
                "error": "文件过大，最大支持 20MB",
                "errors": ["file too large"],
                "charts": [],
                "tables": [],
                "markdown_content": ""
            }
        if not zipfile.is_zipfile(file_path):
            return {
                "success": False,
                "error": "文件格式无效（非有效的 .docx）",
                "errors": ["invalid docx"],
                "charts": [],
                "tables": [],
                "markdown_content": ""
            }
        try:
            with zipfile.ZipFile(file_path, 'r') as zf:
                if 'word/document.xml' not in zf.namelist():
                    return {
                        "success": False,
                        "error": "文件内容无效（缺少文档结构）",
                        "errors": ["missing word/document.xml"],
                        "charts": [],
                        "tables": [],
                        "markdown_content": ""
                    }
        except zipfile.BadZipFile:
            return {
                "success": False,
                "error": "文件已损坏或非有效的 .docx",
                "errors": ["bad zip"],
                "charts": [],
                "tables": [],
                "markdown_content": ""
            }

        chart_service = ChartService()
        # 调用新的D3处理函数
        result = await chart_service.process_word_document_complete_d3(str(file_path))

        if not result["success"]:
            return {
                "success": False,
                "error": "文档处理失败",
                "errors": result["errors"],
                "charts": [],
                "tables": [],
                "markdown_content": ""
            }

        # 将结果同时保存到数据库，便于在模板列表中展示
        try:
            doc_to_create = schemas.ProcessedDocumentCreate(
                filename=file.filename,
                markdown_content=result["markdown_content"],
                tables=result["tables"] or [],
                charts=result["charts"] or [],
            )
            db_document = ProcessedDocument(**doc_to_create.model_dump())
            db.add(db_document)
            db.commit()
            db.refresh(db_document)
        except Exception:
            # 保存失败不阻断前端即时渲染
            db.rollback()
            pass

        # 返回D3兼容的格式（保持现有前端协议）
        return {
            "success": True,
            "markdown_content": result["markdown_content"],
            "tables": result["tables"],
            "charts": result["charts"],
            "errors": result.get("errors", [])
        }

    except Exception as e:
        # 在开发过程中，打印完整的异常以帮助调试
        traceback.print_exc()
        return {
            "success": False,
            "error": f"处理文件时发生严重错误: {str(e)}",
            "errors": [str(e)],
            "charts": [],
            "tables": [],
            "markdown_content": ""
        }
    finally:
        # 清理临时文件
        if file_path.exists():
            file_path.unlink()


@router.get("/documents", response_model=List[schemas.ProcessedDocumentMetadata])
def get_processed_documents(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db),
    # current_user: User = Depends(get_current_user),
):
    """
    获取所有已处理文档的元数据列表
    """
    documents = db.query(ProcessedDocument).order_by(ProcessedDocument.created_at.desc()).offset(skip).limit(limit).all()
    return documents


@router.get("/documents/{document_id}", response_model=schemas.ProcessedDocumentDetail)
def get_processed_document_detail(
    document_id: int, 
    db: Session = Depends(get_db),
    # current_user: User = Depends(get_current_user),
):
    """
    根据ID获取单个已处理文档的完整信息
    """
    document = db.query(ProcessedDocument).filter(ProcessedDocument.id == document_id).first()
    if document is None:
        raise HTTPException(status_code=404, detail="未找到指定的文档")
    return document


@router.delete("/documents/{document_id}")
def delete_processed_document(
    document_id: int,
    db: Session = Depends(get_db),
    # 暂时注释管理员角色认证
    # current_user: User = Depends(require_roles("admin")),
):
    """
    删除指定的文档模板
    """
    document = db.query(ProcessedDocument).filter(ProcessedDocument.id == document_id).first()
    if document is None:
        raise HTTPException(status_code=404, detail="未找到指定的文档")
    
    try:
        db.delete(document)
        db.commit()
        return {"success": True, "message": f"文档 {document.filename} 已成功删除"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除文档时发生错误: {str(e)}")