"""
PDF解析API路由
"""

import shutil
import os
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Dict, Any
from core.logging import get_logger
from services.pdf_parser import CozePDFService
from pathlib import Path
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session
from database.connection import get_db
from models import schemas
from models.database import ProcessedDocument
from core.rate_limit import limit_requests

logger = get_logger(__name__)
router = APIRouter(
    prefix="/api/pdf",
    tags=["PDF Processing"],
)
pdf_service = CozePDFService()


# 项目根目录
project_root = Path(__file__).parent.parent

# 确保静态文件目录存在
results_dir = project_root / "tmp" / "results"
results_dir.mkdir(parents=True, exist_ok=True)

# 挂载静态文件目录
router.mount("/results", StaticFiles(directory=str(results_dir)), name="results")

@router.post("/upload-pdf", dependencies=[Depends(limit_requests(30, 60))])
async def upload_pdf(file: UploadFile = File(...), db: Session = Depends(get_db)):
    """
    上传PDF文件并解析为Markdown
    
    Args:
        file: PDF文件
        db: 数据库会话
        
    Returns:
        解析后的Markdown内容和元数据
    """
    try:
        # 验证文件类型
        if not file.filename.lower().endswith('.pdf'):
            logger.warning(f"文件类型错误: {file.filename}")
            # 返回友好错误响应，而不是抛出HTTPException
            return JSONResponse(content={
                "success": False,
                "markdown_content": "",
                "charts": [],
                "tables": [],
                "message": "只支持PDF文件格式"
            }, status_code=400)
        
        # 创建临时上传目录（如果不存在）
        tmp_dir = project_root / "tmp" / "pdf_uploads"
        tmp_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成临时文件路径
        file_path = tmp_dir / file.filename
        
        # 保存上传的PDF文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 准备结果目录
        output_dir = project_root / "tmp" / "results"
        pdf_filename = Path(file.filename).stem
        result_dir = output_dir / pdf_filename
        
        # 检查结果目录是否存在，如果存在则清空
        if result_dir.exists():
            logger.info(f"结果目录已存在，正在清空: {result_dir}")
            # 清空目录
            for item in result_dir.iterdir():
                if item.is_file():
                    item.unlink()
                elif item.is_dir():
                    shutil.rmtree(item)
        else:
            # 创建结果目录
            result_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建结果目录: {result_dir}")
        
        try:
            # 调用PDF解析服务 - 传递project_root参数
            try:
                result = await pdf_service.parse_pdf_file(str(file_path), str(result_dir), project_root)
                
                # 复制PDF文件到结果目录
                pdf_dest_path = result_dir / file.filename
                shutil.copy2(str(file_path), str(pdf_dest_path))
                logger.info(f"PDF文件已保存到结果目录: {pdf_dest_path}")
                
                # 将解析结果保存到数据库
                try:
                    doc_to_create = schemas.ProcessedDocumentCreate(
                        filename=file.filename,
                        markdown_content=result.markdown_content,
                        tables=[],  # PDF解析暂时没有表格数据
                        charts=[],  # PDF解析暂时没有图表数据
                    )
                    db_document = ProcessedDocument(**doc_to_create.model_dump())
                    db.add(db_document)
                    db.commit()
                    db.refresh(db_document)
                    logger.info(f"PDF解析结果已保存到数据库，文档ID: {db_document.id}")
                except Exception as db_error:
                    logger.error(f"保存解析结果到数据库失败: {str(db_error)}")
                    db.rollback()
                    # 数据库保存失败不阻断前端返回结果
                
                # 构建成功响应
                response_data = {
                    "success": True,
                    "markdown_content": result.markdown_content,
                    "charts": [],
                    "tables": [],
                    "message": "PDF解析成功"
                }
                
            except Exception as parse_error:
                logger.error(f"PDF解析失败: {str(parse_error)}")
                # 解析失败时返回友好响应，而不是500错误
                response_data = {
                    "success": False,
                    "markdown_content": "",
                    "charts": [],
                    "tables": [],
                    "message": f"PDF解析失败: {str(parse_error)}"
                }
            
            return JSONResponse(content=response_data)
            
        finally:
            # 清理临时文件
            if file_path.exists():
                file_path.unlink()
    
    except Exception as e:
        logger.error(f"PDF处理过程中发生错误: {str(e)}", exc_info=True)
        # 捕获所有其他异常并返回友好响应
        return JSONResponse(content={
            "success": False,
            "markdown_content": "",
            "charts": [],
            "tables": [],
            "message": f"处理失败: {str(e)}"
        }, status_code=500)
