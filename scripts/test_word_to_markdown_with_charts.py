#!/usr/bin/env python3
"""
测试Word文档转换为带图表的Markdown功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent  # backend/scripts -> backend -> root
sys.path.insert(0, str(project_root / "backend"))

from services.word_parser import WordToMarkdownParser
from services.word_image_extractor import extract_images_from_docx

def extract_chart_info_from_word(docx_path: str) -> list:
    """从Word文档中提取图表信息"""
    temp_dir = project_root / "temp" / "chart_extraction"
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 提取图片和图表
        images_info = extract_images_from_docx(str(docx_path), str(temp_dir))
        
        # 过滤出图表信息并转换格式
        charts_info = []
        for info in images_info:
            if info.get("type") == "chart":
                chart_info = info.get("chart_info", {})
                xml_summary = chart_info.get("xml_summary", {})
                
                # 转换为新格式
                chart_data = {
                    "chart_id": chart_info.get("chart_id", "unknown"),
                    "xml_length": xml_summary.get("xml_length", 0),
                    "has_data": xml_summary.get("contains_data", False),
                    "has_data_series": xml_summary.get("contains_series", False),
                    "has_categories": xml_summary.get("contains_categories", False),
                    "chart_type_hint": ", ".join(xml_summary.get("chart_type_hints", [])),
                    "xml_file": info.get("xml_file", ""),
                    "xml_preview": chart_info.get("xml_data", ""),
                    "context": chart_info.get("context", "")
                }
                charts_info.append(chart_data)
        
        return charts_info
        
    except Exception as e:
        print(f"提取图表信息时出错：{e}")
        return []


def test_word_to_markdown_with_charts():
    """测试Word文档转换为带图表的Markdown"""
    
    # 创建解析器实例
    parser = WordToMarkdownParser()
    
    # 输入和输出目录
    input_dir = project_root / "tmp"
    output_dir = project_root / "output"
    
    # 确保输出目录存在
    output_dir.mkdir(exist_ok=True)
    
    # 查找所有Word文档
    word_files = []
    for ext in ['*.docx', '*.doc']:
        word_files.extend(input_dir.rglob(ext))
    
    # 过滤掉临时文件
    word_files = [f for f in word_files if not f.name.startswith('~')]
    
    if not word_files:
        print("未找到Word文档")
        return
    
    # 处理每个Word文档
    for doc_path in word_files:
        try:
            print(f"\n{'='*60}")
            print(f"正在处理：{doc_path.name}")
            print(f"{'='*60}")
            
            # 提取图表信息
            charts_info = extract_chart_info_from_word(doc_path)
            
            print(f"发现图表数量：{len(charts_info)}")
            
            # 转换为Markdown，包含图表占位符
            markdown_content = parser.convert_to_markdown_with_chart_placeholders(
                str(doc_path), 
                charts_info
            )
            
            # 生成输出文件名
            output_filename = f"{doc_path.stem}_with_d3.md"
            output_path = output_dir / output_filename
            
            # 保存结果
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            print(f"转换完成：{output_path}")
            
        except Exception as e:
            print(f"处理文档时出错：{str(e)}")
            import traceback
            traceback.print_exc()


def test_single_file():
    """测试单个Word文档"""
    
    # 创建解析器实例
    parser = WordToMarkdownParser()
    
    # 查找测试文件
    test_file = project_root / "tmp" / "2025年1-3月浙江省旅游业数据分析报告-改.docx"
    
    if not test_file.exists():
        print(f"测试文件不存在：{test_file}")
        return
    
    try:
        print(f"正在处理测试文件：{test_file.name}")
        
        # 提取图表信息
        charts_info = extract_chart_info_from_word(test_file)
        
        print(f"发现图表数量：{len(charts_info)}")
        
        # 转换为Markdown，包含图表占位符
        markdown_content = parser.convert_to_markdown_with_chart_placeholders(
            str(test_file), 
            charts_info
        )
        
        # 生成输出文件名
        output_path = project_root / "output" / f"{test_file.stem}_test_with_d3.md"
        
        # 保存结果
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"转换完成：{output_path}")
        
        # 打印图表信息摘要
        if charts_info:
            print(f"\n图表信息摘要：")
            for i, chart in enumerate(charts_info, 1):
                print(f"  图表{i}:")
                print(f"    - 类型：{chart.get('chart_type_hint', '未识别')}")
                print(f"    - XML长度：{chart.get('xml_length', 0)} 字符")
                print(f"    - 包含数据：{'是' if chart.get('has_data', False) else '否'}")
                print(f"    - XML文件：{chart.get('xml_file', '')}")
        
        # 显示文件前几行作为预览
        print(f"\n文件预览（前15行）：")
        print("-" * 50)
        lines = markdown_content.split('\n')
        for i, line in enumerate(lines[:15]):
            print(f"{i+1:2d}: {line}")
        
        if len(lines) > 15:
            print(f"... 还有 {len(lines) - 15} 行")
        
    except Exception as e:
        print(f"处理文件时出错：{str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("测试Word文档转换为带图表的Markdown功能")
    print("=" * 60)
    
    # 测试单个文件
    test_single_file()
    
    # 测试所有文件
    # test_word_to_markdown_with_charts()