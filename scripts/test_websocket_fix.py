#!/usr/bin/env python3
"""
测试 WebSocket 连接问题修复
验证后端服务启动后立即可用性
"""

import asyncio
import websockets
import json
import time
import requests
from typing import Dict, Any

async def test_websocket_connection():
    """测试 WebSocket 连接和编译请求"""
    
    # 1. 首先检查后端健康状态
    print("1. 检查后端健康状态...")
    try:
        health_response = requests.get("http://localhost:8000/api/typst-compile/health", timeout=5)
        health_data = health_response.json()
        print(f"   健康状态: {health_data}")
        
        if not health_data.get('service_ready', False):
            print("   ⚠️  服务尚未就绪，但继续测试...")
        else:
            print("   ✅ 服务已就绪")
    except Exception as e:
        print(f"   ❌ 健康检查失败: {e}")
        return False
    
    # 2. 建立 WebSocket 连接
    print("\n2. 建立 WebSocket 连接...")
    session_id = f"test_session_{int(time.time())}"
    ws_url = f"ws://localhost:8000/ws/compile/{session_id}"
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print(f"   ✅ WebSocket 连接成功: {ws_url}")
            
            # 3. 等待连接确认消息
            print("\n3. 等待连接确认...")
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                print(f"   收到消息: {data}")
                
                if data.get('type') == 'connection_established':
                    service_ready = data.get('service_ready', False)
                    print(f"   服务就绪状态: {service_ready}")
                
            except asyncio.TimeoutError:
                print("   ⚠️  未收到连接确认消息")
            
            # 4. 发送编译请求
            print("\n4. 发送编译请求...")
            compile_request = {
                "type": "compile",
                "content": "# 测试文档\n\n这是一个测试编译请求。",
                "options": {
                    "enable_cache": True,
                    "enable_incremental": True
                },
                "force_recompile": False
            }
            
            await websocket.send(json.dumps(compile_request))
            print("   ✅ 编译请求已发送")
            
            # 5. 等待编译响应
            print("\n5. 等待编译响应...")
            response_count = 0
            start_time = time.time()
            
            while response_count < 10 and (time.time() - start_time) < 30:  # 最多等待30秒
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    response_count += 1
                    
                    msg_type = data.get('type')
                    print(f"   [{response_count}] 收到: {msg_type}")
                    
                    if msg_type == 'compilation_started':
                        print("       ✅ 编译已开始")
                    elif msg_type == 'compilation_progress':
                        stage = data.get('stage', 'unknown')
                        progress = data.get('progress', 0)
                        print(f"       📊 编译进度: {stage} - {progress:.1%}")
                    elif msg_type == 'compilation_complete':
                        print("       ✅ 编译完成!")
                        vector_data = data.get('result', {}).get('vector_data', '')
                        print(f"       向量数据长度: {len(vector_data)} 字符")
                        break
                    elif msg_type == 'compilation_error':
                        error = data.get('error', 'Unknown error')
                        retry_after = data.get('retry_after')
                        print(f"       ❌ 编译错误: {error}")
                        if retry_after:
                            print(f"       🔄 建议 {retry_after} 秒后重试")
                        break
                    elif msg_type == 'pong':
                        print("       💓 心跳响应")
                    
                except asyncio.TimeoutError:
                    print("   ⏰ 等待响应超时")
                    break
                except Exception as e:
                    print(f"   ❌ 接收消息失败: {e}")
                    break
            
            if response_count == 0:
                print("   ❌ 未收到任何响应 - 这是原始问题!")
                return False
            else:
                print(f"   ✅ 总共收到 {response_count} 个响应")
                return True
                
    except Exception as e:
        print(f"   ❌ WebSocket 连接失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 WebSocket 连接问题修复测试")
    print("=" * 50)
    
    success = await test_websocket_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试通过 - WebSocket 连接和编译正常工作!")
    else:
        print("❌ 测试失败 - 仍存在连接或编译问题")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
