#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Word文档转换为D3.js图表配置的端到端测试
"""

import os
import sys
import json
import asyncio
from pathlib import Path
import shutil

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))

from services.chart_service import ChartService

async def test_complete_d3_pipeline():
    """测试完整的D3.js流水线"""
    print("🚀 测试完整的Word到D3.js流水线")
    print("=" * 80)
    
    # 查找Word文档
    word_dir = current_dir.parent.parent / "tmp"
    word_files = []
    
    for ext in ['*.docx', '*.doc']:
        word_files.extend(word_dir.rglob(ext))
    
    # 过滤掉临时文件
    word_files = [f for f in word_files if not f.name.startswith('~')]
    
    if not word_files:
        print("❌ 未找到Word文档")
        return
    
    print(f"📁 找到 {len(word_files)} 个Word文档")
    
    # 创建输出目录
    output_dir = current_dir.parent / "temp" / "d3_pipeline_test"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建图表服务
    chart_service = ChartService()
    
    # 测试第一个Word文档
    test_file = word_files[0]
    print(f"\n📄 测试文档: {test_file.name}")
    print("-" * 60)
    
    try:
        # 处理Word文档
        result = await chart_service.process_word_document_complete_d3(str(test_file))
        
        print(f"✅ 处理成功: {result['success']}")
        print(f"📊 图表数量: {len(result.get('charts', []))}")
        print(f"📋 表格数量: {len(result.get('tables', []))}")
        print(f"❌ 错误数量: {len(result.get('errors', []))}")
        
        if result.get('errors'):
            print("❌ 错误详情:")
            for error in result['errors']:
                print(f"   - {error}")
        
        # 保存结果
        if result['success']:
            # 保存Markdown内容
            markdown_file = output_dir / f"{test_file.stem}_d3.md"
            with open(markdown_file, 'w', encoding='utf-8') as f:
                f.write(result['markdown_content'])
            print(f"📝 Markdown已保存: {markdown_file}")
            
            # 保存图表配置
            charts_file = output_dir / f"{test_file.stem}_charts.json"
            with open(charts_file, 'w', encoding='utf-8') as f:
                json.dump(result['charts'], f, indent=2, ensure_ascii=False)
            print(f"📊 图表配置已保存: {charts_file}")
            
            # 保存表格数据
            tables_file = output_dir / f"{test_file.stem}_tables.json"
            with open(tables_file, 'w', encoding='utf-8') as f:
                json.dump(result['tables'], f, indent=2, ensure_ascii=False)
            print(f"📋 表格数据已保存: {tables_file}")
            
            # 详细分析图表配置
            print(f"\n📊 图表配置分析:")
            for i, chart in enumerate(result.get('charts', [])):
                config = chart.get('config', {})
                print(f"   图表 {i+1}:")
                print(f"     - ID: {chart.get('id', 'N/A')}")
                print(f"     - 标题: {config.get('title', 'N/A')}")
                print(f"     - 类型: {config.get('type', 'N/A')}")
                
                if config.get('type') == 'combo':
                    print(f"     - 分类数量: {len(config.get('categories', []))}")
                    print(f"     - 数据集数量: {len(config.get('datasets', []))}")
                    for j, dataset in enumerate(config.get('datasets', [])):
                        print(f"       数据集 {j+1}: {dataset.get('name', 'N/A')} ({dataset.get('type', 'N/A')})")
                
                elif config.get('type') == 'nested-pie':
                    data = config.get('data', {})
                    outer_data = data.get('outer', [])
                    inner_data = data.get('inner', [])
                    print(f"     - 外圈数据: {len(outer_data)} 项")
                    print(f"     - 内圈数据: {len(inner_data)} 项")
                    if outer_data:
                        print(f"       外圈: {', '.join([item['name'] for item in outer_data])}")
                    if inner_data:
                        print(f"       内圈: {', '.join([item['name'] for item in inner_data])}")
                
                elif config.get('type') == 'simple-pie':
                    datasets = config.get('datasets', [])
                    print(f"     - 数据项: {len(datasets)} 个")
                    if datasets:
                        print(f"       项目: {', '.join([item['name'] for item in datasets])}")
                
                elif config.get('type') == 'error':
                    print(f"     - ❌ 错误: {config.get('error', 'N/A')}")
                
                print()
            
            # 生成前端兼容性报告
            print(f"📱 前端兼容性分析:")
            compatible_charts = 0
            for chart in result.get('charts', []):
                config = chart.get('config', {})
                chart_type = config.get('type', 'unknown')
                
                if chart_type in ['combo', 'nested-pie', 'simple-pie']:
                    compatible_charts += 1
                    print(f"   ✅ {chart.get('id', 'N/A')}: {chart_type} - 兼容")
                else:
                    print(f"   ❌ {chart.get('id', 'N/A')}: {chart_type} - 不兼容")
            
            compatibility_rate = (compatible_charts / len(result.get('charts', []))) * 100 if result.get('charts') else 0
            print(f"   📊 兼容率: {compatibility_rate:.1f}% ({compatible_charts}/{len(result.get('charts', []))})")
            
        else:
            print(f"❌ 处理失败")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎉 测试完成！")
    print(f"📁 输出目录: {output_dir}")

if __name__ == "__main__":
    asyncio.run(test_complete_d3_pipeline()) 