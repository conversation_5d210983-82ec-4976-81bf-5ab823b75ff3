#!/usr/bin/env python3
"""
Test the streaming AI endpoint
"""

import asyncio
import aiohttp
import json
from pathlib import Path
import sys

# Add project root to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))

async def test_streaming_endpoint():
    """Test the streaming AI endpoint"""
    
    url = "http://localhost:8000/api/prose/generate"
    
    # Test payload
    payload = {
        "prompt": "Please improve this text: AI Editor is a good tool.",
        "option": "improve",
        "command": "",
        "context": "",
        "selected_text": "AI Editor is a good tool.",
        "template_type": "general",
        "current_section": ""
    }
    
    print("🚀 Testing streaming endpoint...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=payload,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'text/plain'
                }
            ) as response:
                
                print(f"Response status: {response.status}")
                print(f"Response headers: {dict(response.headers)}")
                print("-" * 50)
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ Error: {error_text}")
                    return
                
                print("📡 Streaming response:")
                chunk_count = 0
                total_content = ""
                
                async for line in response.content:
                    if line:
                        chunk = line.decode('utf-8')
                        chunk_count += 1
                        
                        # Parse SSE format
                        if chunk.startswith('event: '):
                            event_type = chunk[7:].strip()
                            print(f"[Chunk {chunk_count}] Event: {event_type}")
                        elif chunk.startswith('data: '):
                            data = chunk[5:].strip()
                            if data:
                                total_content += data
                                print(f"[Chunk {chunk_count}] Data: {data[:100]}{'...' if len(data) > 100 else ''}")
                        elif chunk.strip() == '':
                            print(f"[Chunk {chunk_count}] Empty line (event separator)")
                        else:
                            print(f"[Chunk {chunk_count}] Raw: {chunk.strip()}")
                
                print("-" * 50)
                print(f"✅ Streaming completed!")
                print(f"Total chunks: {chunk_count}")
                print(f"Total content length: {len(total_content)}")
                print(f"Content preview: {total_content[:200]}{'...' if len(total_content) > 200 else ''}")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing AI Streaming Endpoint")
    print("=" * 50)
    asyncio.run(test_streaming_endpoint())