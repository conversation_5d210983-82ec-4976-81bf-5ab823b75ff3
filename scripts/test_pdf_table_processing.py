#!/usr/bin/env python3
"""
表格处理逻辑测试脚本
直接测试_process_workflow_result方法的表格处理功能
"""

import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from services.pdf_parser import CozePDFService


def create_mock_data() -> Dict[str, Any]:
    """创建模拟的解析数据

    Returns:
        Dict[str, Any]: 包含parsed_array和possible_titles的模拟数据
    """
    # 模拟数据包含一个表格
    parsed_array = [
        {"id": 0, "label": "text", "content": "# 测试文档"},
        {"id": 1, "label": "text", "content": "## 表格测试"},
        {"id": 2, "label": "text", "content": "| 姓名 | 年龄 | 职业 |"},
        {"id": 3, "label": "text", "content": "| --- | --- | --- |"},
        {"id": 4, "label": "text", "content": "| 张三 | 25 | 工程师 |"},
        {"id": 5, "label": "text", "content": "| 李四 | 30 | 设计师 |"},
        {"id": 6, "label": "text", "content": "| 王五 | 35 | 产品经理 |"},
        {"id": 7, "label": "text", "content": "注: 数据来源于测试系统"},
        {"id": 8, "label": "text", "content": "## 非表格内容"},
        {"id": 9, "label": "text", "content": "这是表格后的普通文本内容。"}
    ]

    possible_titles = [
        {"id": "0", "label": "title", "content": "# 测试文档"},
        {"id": "1", "label": "title", "content": "## 表格测试"},
        {"id": "8", "label": "title", "content": "## 非表格内容"}
    ]

    return {
        "parsed_array": parsed_array,
        "possible_titles": possible_titles
    }


def test_table_processing():
    """测试表格处理逻辑"""
    print("🚀 开始测试表格处理逻辑")

    # 创建服务实例
    pdf_service = CozePDFService()

    # 创建模拟数据
    mock_data = create_mock_data()
    print("✅ 模拟数据创建成功")

    try:
        # 调用处理方法
        print("🔍 调用_process_workflow_result方法...")
        result = pdf_service._process_workflow_result(mock_data)

        # 打印结果
        print("✅ 处理完成，结果如下:")
        print("\n--- 生成的Markdown内容 ---")
        print(result.markdown_content)
        print("--- Markdown内容结束 ---")

        # 检查表格后是否有空行
        markdown_lines = result.markdown_content.split('\n')
        table_end_index = -1
        for i, line in enumerate(markdown_lines):
            if line.startswith("| 王五 "):
                table_end_index = i
                break

        if table_end_index != -1 and table_end_index + 1 < len(markdown_lines):
            if markdown_lines[table_end_index + 1] == "":
                print("✅ 表格后正确添加了空行分隔")
            else:
                print("❌ 表格后未添加空行分隔")
        else:
            print("❌ 未找到表格结束行")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return

    print("\n🎉 表格处理逻辑测试完成!")


if __name__ == "__main__":
    test_table_processing()