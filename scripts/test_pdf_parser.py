#!/usr/bin/env python3
"""
PDF解析功能测试脚本
只进行一轮完整测试并输出解析结果
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from services.pdf_parser import CozePDFService, PDFParseResult


async def test_pdf_parser(pdf_file_path: str):
    """测试PDF解析功能

    Args:
        pdf_file_path: PDF文件路径
    """
    print(f"🚀 开始测试PDF解析功能，文件: {pdf_file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(pdf_file_path):
        print(f"❌ 错误: 文件 {pdf_file_path} 不存在")
        return
    
    # 创建服务实例
    pdf_service = CozePDFService()
    
    # 打印服务配置信息
    print(f"✅ PDF解析服务初始化成功")
    print(f"   - API Token: {pdf_service.api_token[:20]}...")
    print(f"   - 工作流ID: {pdf_service.workflow_id}")
    print(f"   - 上传URL: {pdf_service.upload_url}")
    print(f"   - 工作流URL: {pdf_service.workflow_url}")
    
    try:
        # 测试完整解析流程 - 不需要传递output_dir参数
        print("\n🔄 测试完整解析流程(上传+解析)...")
        result = await pdf_service.parse_pdf_file(pdf_file_path)
        print(f"✅ 解析完成")
        
        # 验证解析结果
        print("\n📊 验证解析结果...")
        if not result.markdown_content:
            print("❌ 警告: Markdown内容为空")
        else:
            print(f"✅ Markdown内容长度: {len(result.markdown_content)} 字符")
            print(f"   前50个字符: {result.markdown_content[:50]}...")
        
        # 打印保存位置
        pdf_filename = Path(pdf_file_path).stem
        output_dir = Path("../tmp/results") / pdf_filename
        output_file = output_dir / f"{pdf_filename}.md"
        print(f"✅ 解析结果已保存到: {output_file}")
        
        if not result.parsed_array:
            print("❌ 警告: parsed_array为空")
        else:
            print(f"✅ parsed_array包含 {len(result.parsed_array)} 个元素")
            print(f"   第一个元素: {result.parsed_array[0]}")
        
        if not result.possible_titles:
            print("❌ 警告: possible_titles为空")
        else:
            print(f"✅ possible_titles包含 {len(result.possible_titles)} 个标题")
            print(f"   第一个标题: {result.possible_titles[0]}")
        
        # 可选：打印解析的markdown内容
        # print("\n📝 解析的Markdown内容:\n")
        # print(result.markdown_content)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return
    
    print("\n🎉 PDF解析功能测试完成!")
    # print("使用 /upload-pdf 接口上传PDF文件")
    # print("\n📝 API端点:- POST /upload-pdf - 上传并解析PDF")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        pdf_file_path = sys.argv[1]
    else:
        # 使用默认PDF文件路径
        # 使用基于项目根目录的绝对路径
        project_root = Path(__file__).parent.parent
        pdf_file_path = project_root / "tmp" / "pdf_uploads" / "test12581.pdf"
        pdf_file_path = str(pdf_file_path)

        print(f"使用默认PDF文件路径: {pdf_file_path}")
    
    asyncio.run(test_pdf_parser(pdf_file_path))