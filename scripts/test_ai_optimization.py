#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""专门测试AI优化功能"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))

from services.ai_chart_optimizer import AIChartOptimizer

async def test_ai_optimization():
    """测试AI优化功能"""
    print("🤖 AI图表优化功能测试")
    print("=" * 60)
    
    # 初始化优化器
    optimizer = AIChartOptimizer()
    
    print(f"🔧 优化器状态:")
    print(f"  API密钥状态: {'✅ 已配置' if optimizer.has_api_key else '❌ 未配置'}")
    print(f"  客户端状态: {'✅ 已初始化' if optimizer.client else '❌ 未初始化'}")
    
    if optimizer.has_api_key:
        print(f"  模型名称: {optimizer.llm_settings.model_name}")
        print(f"  API地址: {optimizer.llm_settings.base_url}")
    
    # 准备测试数据
    test_parsed_data = {
        "chart_type": "pie",
        "categories": ["省外游客", "县内游", "跨县游", "跨市游"],
        "values": [[23.9, 50.6, 21.9, 27.4]],
        "series": [{"name": "游客分布"}]
    }
    
    test_base_config = {
        "type": "pie",
        "data": {
            "labels": test_parsed_data["categories"],
            "datasets": [{
                "data": test_parsed_data["values"][0],
                "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"]
            }]
        },
        "options": {
            "responsive": True
        }
    }
    
    print(f"\n📊 测试数据:")
    print(f"  图表类型: {test_parsed_data['chart_type']}")
    print(f"  分类数量: {len(test_parsed_data['categories'])}")
    print(f"  数据系列: {len(test_parsed_data['values'])}")
    
    print(f"\n🎨 开始AI优化...")
    
    try:
        # 调用AI优化
        optimized_config = await optimizer.optimize_chart_config(
            parsed_data=test_parsed_data,
            base_config=test_base_config,
            context="浙江省旅游客源结构分析报告",
            target_audience="business"
        )
        
        print(f"✅ AI优化完成!")
        
        # 分析优化结果
        print(f"\n📋 优化结果分析:")
        
        # 检查是否有AI优化的痕迹
        ai_optimizations = []
        
        # 检查标题
        if "plugins" in optimized_config.get("options", {}):
            plugins = optimized_config["options"]["plugins"]
            if "title" in plugins and plugins["title"].get("text"):
                ai_optimizations.append(f"标题优化: {plugins['title']['text']}")
        
        # 检查颜色方案
        if "data" in optimized_config and "datasets" in optimized_config["data"]:
            dataset = optimized_config["data"]["datasets"][0]
            if "backgroundColor" in dataset:
                ai_optimizations.append(f"色彩优化: {len(dataset['backgroundColor'])} 种颜色")
        
        # 检查布局
        if "layout" in optimized_config.get("options", {}):
            ai_optimizations.append("布局优化: 已应用")
        
        # 检查动画
        if "animation" in optimized_config.get("options", {}):
            duration = optimized_config["options"]["animation"].get("duration", 0)
            ai_optimizations.append(f"动画优化: {duration}ms")
        
        if ai_optimizations:
            print(f"🎯 检测到AI优化:")
            for opt in ai_optimizations:
                print(f"  - {opt}")
        else:
            print(f"🔄 使用了预设优化策略")
        
        # 输出配置对比
        print(f"\n📄 配置对比:")
        print(f"原始配置键数: {len(test_base_config)}")
        print(f"优化配置键数: {len(optimized_config)}")
        
        # 保存结果
        output_file = Path(__file__).parent.parent / "output" / "ai_optimization_test.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "original_config": test_base_config,
                "optimized_config": optimized_config,
                "ai_optimizations": ai_optimizations,
                "has_api_key": optimizer.has_api_key
            }, f, indent=2, ensure_ascii=False)
        
        print(f"💾 结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_suggestions():
    """测试AI建议生成"""
    print(f"\n🧠 AI建议生成测试")
    print("=" * 50)
    
    optimizer = AIChartOptimizer()
    
    # 模拟数据分析
    analysis = {
        "chart_type": "pie",
        "data_points_count": 4,
        "series_count": 1,
        "categories_count": 4,
        "value_ranges": [{"min": 21.9, "max": 50.6, "avg": 31.0}],
        "data_patterns": ["skewed_distribution"],
        "suggested_purpose": "distribution"
    }
    
    test_data = {
        "chart_type": "pie",
        "categories": ["省外游客", "县内游", "跨县游", "跨市游"],
        "values": [[23.9, 50.6, 21.9, 27.4]]
    }
    
    try:
        suggestions = await optimizer._get_ai_suggestions(
            data=test_data,
            analysis=analysis,
            context="浙江省旅游客源结构分析",
            target_audience="business"
        )
        
        print(f"✅ AI建议生成成功!")
        print(f"📋 建议内容:")
        
        for key, value in suggestions.items():
            print(f"  {key}: {type(value).__name__}")
            if isinstance(value, dict) and len(value) <= 3:
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            elif isinstance(value, list) and len(value) <= 3:
                for i, item in enumerate(value):
                    print(f"    {i+1}. {item}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI建议生成失败: {e}")
        return False

if __name__ == "__main__":
    async def main():
        # 测试AI优化
        opt_result = await test_ai_optimization()
        
        # 测试AI建议
        sug_result = await test_ai_suggestions()
        
        print(f"\n📋 测试总结")
        print("=" * 50)
        print(f"AI优化测试: {'✅ 通过' if opt_result else '❌ 失败'}")
        print(f"AI建议测试: {'✅ 通过' if sug_result else '❌ 失败'}")
        
        if opt_result and sug_result:
            print(f"\n🎉 AI功能完全正常！")
            print(f"💡 系统现在可以提供AI驱动的图表优化建议")
        elif opt_result or sug_result:
            print(f"\n⚠️ 部分AI功能正常，建议检查配置")
        else:
            print(f"\n❌ AI功能异常，请检查API配置")
    
    asyncio.run(main()) 