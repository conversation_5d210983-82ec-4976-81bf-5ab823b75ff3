#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Typst模板应用功能
转换图片和表格格式 在下一步的图片图表集成中引入，暂不分析此脚本
测试apply_template_to_content方法逻辑是否正确应用模板并转换图片和表格格式
"""

import os
import sys
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from services.markdown_to_typst_service import MarkdownToTypstService


def test_apply_template_to_content():
    """测试应用模板到内容的功能（新的无状态方法）"""
    print("开始测试应用模板到内容的功能...")

    # 创建测试服务实例
    service = MarkdownToTypstService()
    test_template_name = "travel"
    
    try:
        # 使用绝对路径确保正确性
        project_root = Path(__file__).parent.parent
        
        # 1. 创建测试模板文件（如果不存在）
        template_dir = project_root / "assets" / "templates" / test_template_name
        template_file_path = template_dir / f"{test_template_name}.typ"
        if not os.path.exists(template_file_path):
            # 如果模板文件不存在，创建一个临时的测试模板
            print(f"注意：模板文件 {template_file_path} 不存在，创建测试模板...")
            if not os.path.exists(template_dir):
                os.makedirs(template_dir, exist_ok=True)
            
            test_template_content = """
#let reimage(image, capzh) = figure(
  image,
  supplement: none,
  kind: "reimage",
  caption: metadata(capzh),
)

#show figure.where(kind: "reimage"): it => {
  let (capzh) = it.caption.body.value
  let c = it.counter.display("1")
  it.body
  parbreak()
  [图 #c #capzh]
}

#let retable(capzh, table) = figure(
  table,
  supplement: none,
  kind: "retable",
  caption: metadata(capzh),
)
#show figure.where(kind: "retable"): it => {
  let (capzh) = it.caption.body.value
  let c = it.counter.display("1")
  it.body
  parbreak()
  [表 #c #capzh]
}
            """
            with open(template_file_path, "w", encoding="utf-8") as f:
                f.write(test_template_content)
        
        # 2. 创建测试Typst内容
        test_typ_content = '''
== 三､各县市区情况
#label("三各县市区情况");
从各嘉兴市各县(市､区)情况看,全域旅游人数列前三的 分别是南湖区(39.6 万人次)､ 桐乡市(31.9 万人次)､海宁 市(31.8 万人次),相当于全市总量的20.4%､16.4%､16.4%｡ 全域旅游人数同比增速列前三的是海盐县(15.5%)､海宁市 2 全市118 家"爆款"培育旅游产品到访人数的数据来源于移动和电信两家运营商手机信令大数据监测｡ 2 (8.9%)､平湖市(-1.8%)｡ 从各县(市､区)情况看,过夜游客人数列前三的分别是桐 乡市(15.4 万人次)､南湖区(15.2 万人次)､嘉善县(14.5 万人次),相当于全市总量的17.8%､17.6%､16.8%｡全域旅游 人数同比增速列前三的是海盐县(5.6%)､平湖市(4.8%)､海 宁市(1.2%)｡ #image("images/60cad782-bba7-4e47-8a93-02a57e303861.jpeg",alt:"image"); 图1 2025 年端午假期嘉兴市各县(市､区)全域旅游人数 #image("images/cf1a2406-7c7c-4361-a111-f2c1b59aa170.jpeg",alt:"image"); 图2 2025 年端午假期嘉兴市各县(市､区)过夜游客人数 3 表4 2025 年端午假期嘉兴市各县(市､区)旅游市场情况表

#table(align:(auto,auto,auto,auto,auto,),columns:5,table.header([设区市],[全域旅游人 数(万人次)],[同比增长(%)],[过夜游客人数 (万人次)],[同比增长 (%)],),[南湖区],[39.6],[6.5],[15.2],[9.8],[秀洲区],[27.1],[3.1],[10.7],[-2.1],[嘉善县],[28.4],[-1.6],[14.5],[-7.7],[海盐县],[16.7],[-15.9],[7.4],[-17.5],[海宁市],[31.8],[32.4],[14.2],[15.8],[平湖市],[18.6],[-23.2],[9.1],[-17.0],[桐乡市],[31.9],[-14.6],[15.4],[-11.1],));
== 四､客源画像
#label("四客源画像");
1.客源结构｡全市省内游客21.1 万人次,同比增长14.1%, 增速高于全市平均水平5.2 个百分点,占全域旅游人数的66.3%, 占比较上年提高3 个百分点;省内游客构成中,县内游､跨县游 和跨设区市游分别占34.8%､14.1%和51.1%｡与上年端午假期相 比,跨市游占比提高了4.2 个百分点,县内游和跨县游占比分别 下降了3.5 和0.7 个百分点｡省外游客10.7 万人次,同比增长 0.1%,增速低于全市平均水平8.8 个百分点,占全域旅游人数的 36.7%,占比较上年下降3 个百分点｡从省外客源地来看,游客 量占省外游客比重列前三的是:上海 (18.2%)､江苏(18.2%)､ 广东(7.1%)｡ #image("images/36f3bef4-ea0b-4b40-8535-bfadfa2965a3.jpeg",alt:"image"); 图3 2025 年端午假期海宁市接待游客客源结构 表5 2025 年端午假期海宁市接待省外游客客源地TOP10

#table(align:(auto,auto,auto,auto,auto,),columns:5,table.header([位次],[客源地],[游客量 (万人次)],[占省外游客量 的比重(%)],[占全市接待游客 量的比重(%)],),[1],[上海],[2.0],[18.2],[6.1],[2],[江苏],[2.0],[18.2],[6.1],[3],[广东],[0.8],[7.1],[2.4],[4],[安徽],[0.7],[6.7],[2.2],[5],[山东],[0.7],[6.2],[2.1],[6],[河南],[0.6],[5.2],[1.8],[7],[云南],[0.5],[4.8],[1.6],[8],[江西],[0.5],[4.6],[1.5],[9],[四川],[0.4],[3.6],[1.2],[10],[湖北],[0.3],[2.9],[1.0],));2.旅游时长｡全市过夜游占44.7%,比上年同期下降3.4 个 百分点,一日游占55.3%｡ 5 #image("images/a6de708b-b83c-4343-9b28-0296ae64500f.jpeg",alt:"image"); 图4 2025 年端午假期海宁市游客过夜游/一日游构成 3.性别年龄｡游客男性比例较高,占63.8%;女性占36.2%｡ 从年龄构成看,23 岁-45 岁为主要游客群体,占54.3%;其次为 46-60 岁,占25.5%;60 岁以上占7.6%,17-22 岁占8.8%,16 岁 以下占0.4%｡ #image("images/660d2828-ae95-4c55-b110-6a0bc64082cf.jpeg",alt:"image"); 图5 2025 年端午假期海宁市游客性别构成 6 #image("images/365315d7-a6f8-4350-82c5-8822d4cc0ecb.jpeg",alt:"image"); 图6 2025 年端午假期海宁市游客年龄构成 7
'''
        
        print(f"创建测试内容，长度: {len(test_typ_content)}")
        
        # 3. 调用apply_template_to_content方法
        print(f"应用模板 {test_template_name} 到内容...")
        result_content = service.apply_template_to_content(test_template_name, test_typ_content)
        
        # 4. 验证结果
        if result_content:
            print("模板应用成功!")
            
            # 检查内容是否包含模板内容
            if "#let reimage(image, capzh)" in result_content:
                print("✓ 内容包含模板内容")
            else:
                print("✗ 内容不包含模板内容")
            
            # 检查图片格式是否转换成功
            if "#reimage(image(" in result_content:
                print("✓ 图片格式转换成功")
                # 统计转换的图片数量
                image_count = result_content.count("#reimage(image(")
                print(f"   转换了 {image_count} 个图片")
            else:
                print("✗ 图片格式转换失败")
            
            # 检查表格格式是否转换成功
            if "#retable(" in result_content:
                print("✓ 表格格式转换成功")
                # 统计转换的表格数量
                table_count = result_content.count("#retable(")
                print(f"   转换了 {table_count} 个表格")
            else:
                print("✗ 表格格式转换失败")
            
            # 输出部分转换后的内容作为示例
            print("\n转换后的内容示例:")
            # 找到第一个reimage和retable的位置，显示附近内容
            reimage_pos = result_content.find("#reimage(image(")
            retable_pos = result_content.find("#retable(")
            
            if reimage_pos != -1:
                # 显示reimage附近的内容
                start = max(0, reimage_pos - 20)
                end = min(len(result_content), reimage_pos + 100)
                print(f"\n图片转换示例:\n{result_content[start:end]}...")
            
            if retable_pos != -1:
                # 显示retable附近的内容
                start = max(0, retable_pos - 20)
                end = min(len(result_content), retable_pos + 100)
                print(f"\n表格转换示例:\n{result_content[start:end]}...")
            
            # 可选：保存结果到文件以供检查
            save_result = True
            if save_result:
                test_user_dir = project_root / "tmp" / "results" / "test_template_user"
                if not os.path.exists(test_user_dir):
                    os.makedirs(test_user_dir, exist_ok=True)
                
                result_file_path = test_user_dir / "result.typ"
                with open(result_file_path, "w", encoding="utf-8") as f:
                    f.write(result_content)
                print(f"\n结果已保存到: {result_file_path}")
                print("建议手动检查文件内容以确认转换效果是否符合预期。")
            
        else:
            print("模板应用失败!")
            # 尝试读取日志以获取更多信息
            try:
                import logging
                logging.basicConfig(level=logging.INFO)
                print("请查看应用程序日志以获取更多错误信息。")
            except:
                pass
        
        print("测试完成！")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理测试文件（可选，根据需要保留或删除）
        cleanup = False  # 设置为True以清理测试文件
        if cleanup:
            print("清理测试文件...")
            # Cast to Path to resolve type issues
            project_path = Path(project_root)
            test_user_dir = project_path / "tmp" / "results" / "test_template_user"
            if test_user_dir.exists():
                shutil.rmtree(test_user_dir)
                print(f"删除测试用户目录: {test_user_dir}")
            # 不删除模板文件，因为它可能是实际使用的文件


if __name__ == "__main__":
    test_apply_template_to_content()