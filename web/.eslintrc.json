{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"no-restricted-syntax": ["error", {"selector": "CallExpression[callee.object.name='localStorage'][callee.property.name='getItem']:not([arguments.0.value='access_token']):not([arguments.0.value='refresh_token'])", "message": "Only 'access_token' and 'refresh_token' should be accessed from localStorage. Use getAccessToken() or getRefreshToken() from src/utils/auth.ts instead."}, {"selector": "CallExpression[callee.object.name='localStorage'][callee.property.name='setItem']:not([arguments.0.value='access_token']):not([arguments.0.value='refresh_token'])", "message": "Only 'access_token' and 'refresh_token' should be stored in localStorage. Use setAccessToken() or setRefreshToken() from src/utils/auth.ts instead."}, {"selector": "CallExpression[callee.object.name='localStorage'][callee.property.name='removeItem']:not([arguments.0.value='access_token']):not([arguments.0.value='refresh_token'])", "message": "Only 'access_token' and 'refresh_token' should be removed from localStorage. Use clearAuthTokens() from src/utils/auth.ts instead."}, {"selector": "Literal[value=/^<PERSON><PERSON>\\s/i]", "message": "Avoid hardcoded 'Bearer' strings. Use getAuthHeaders() from src/utils/auth.ts instead."}, {"selector": "TemplateLiteral[quasis.0.value.raw=/^<PERSON><PERSON>\\s/i]", "message": "Avoid hardcoded 'Bearer' strings in templates. Use getAuthHeaders() from src/utils/auth.ts instead."}, {"selector": "VariableDeclarator[init.type='Literal'][init.value=/^<PERSON><PERSON>\\s/i]", "message": "Avoid hardcoded 'Bearer' strings in variables. Use getAuthHeaders() from src/utils/auth.ts instead."}, {"selector": "VariableDeclarator[init.type='TemplateLiteral'][init.quasis.0.value.raw=/^Bearer\\s/i]", "message": "Avoid hardcoded 'Bearer' strings in variables. Use getAuthHeaders() from src/utils/auth.ts instead."}, {"selector": "Property[key.name='Authorization'][value.type='Literal']", "message": "Authorization headers should not be hardcoded. Use getAuthHeaders() from src/utils/auth.ts instead."}, {"selector": "Property[key.name='Authorization'][value.type='TemplateLiteral'][value.quasis.0.value.raw=/^Bearer\\s/i]", "message": "Authorization headers should not use hardcoded templates. Use getAuthHeaders() from src/utils/auth.ts instead."}], "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}]}, "overrides": [{"files": ["src/**/auth*.ts", "src/**/api-client.ts", "src/**/ai-api.ts", "src/**/middleware.ts", "src/**/error-handler.ts", "src/lib/**/*.ts"], "rules": {"no-restricted-syntax": "off"}}, {"files": ["src/**/*.test.ts", "src/**/*.spec.ts"], "rules": {"no-restricted-syntax": "off", "@typescript-eslint/no-explicit-any": "off"}}]}