#!/usr/bin/env node

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { copyFileSync, mkdirSync, existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const webRoot = dirname(__dirname);

/**
 * 复制 Typst WebAssembly 文件到 public 目录
 * 确保前端可以正确加载 WASM 模块
 */
function copyTypstWasm() {
  console.log('🔧 正在复制 Typst WebAssembly 文件...');
  
  // 源文件路径
  const wasmSourcePath = join(
    webRoot, 
    'node_modules/@myriaddreamin/typst-ts-renderer/pkg/typst_ts_renderer_bg.wasm'
  );
  
  // 目标目录和文件路径
  const publicTypstDir = join(webRoot, 'public/typst');
  const wasmTargetPath = join(publicTypstDir, 'typst_ts_renderer_bg.wasm');
  
  try {
    // 检查源文件是否存在
    if (!existsSync(wasmSourcePath)) {
      console.error('❌ 错误: 找不到 Typst WASM 文件');
      console.error(`   预期位置: ${wasmSourcePath}`);
      console.error('   请确保已安装 @myriaddreamin/typst-ts-renderer 包');
      process.exit(1);
    }
    
    // 创建目标目录
    mkdirSync(publicTypstDir, { recursive: true });
    
    // 复制文件
    copyFileSync(wasmSourcePath, wasmTargetPath);
    
    console.log('✅ Typst WASM 文件复制成功!');
    console.log(`   源文件: ${wasmSourcePath}`);
    console.log(`   目标文件: ${wasmTargetPath}`);
    
  } catch (error) {
    console.error('❌ 复制 WASM 文件时出错:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则执行复制操作
if (import.meta.url === `file://${process.argv[1]}`) {
  copyTypstWasm();
}

export { copyTypstWasm };
