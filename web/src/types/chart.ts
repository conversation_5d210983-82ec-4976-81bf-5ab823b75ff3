export interface Chart {
  chartId?: string
  id?: string
  title?: string
  type: string
  data?: Record<string, unknown>
  datasets?: ChartDataset[]
  categories?: string[]
  error?: string
}

export interface ChartDataset {
  label?: string
  data: number[]
  value?: number
  name?: string
  type?: string
}

export interface Table {
  id: string
  title?: string
  headers: string[]
  rows: string[][]
}

export interface DocumentTemplate {
  id: number
  filename: string
  created_at: string
  markdown_content?: string
  tables: Table[]
  charts: Chart[]
}

export interface WordParseResult {
  success: boolean
  markdown_content: string
  charts: Chart[]
  tables: Table[]
  errors?: string[]
}