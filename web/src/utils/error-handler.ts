/**
 * 统一错误处理工具
 * 提供标准化的错误响应格式和处理逻辑
 */

// 统一错误响应接口
export interface UnifiedErrorResponse {
  error: {
    code: string
    message: string
    details?: unknown
    timestamp: string
    request_id?: string
  }
}

// 错误类型定义
export enum ErrorType {
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  VALIDATION = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  INTERNAL = 'INTERNAL_ERROR',
  NETWORK = 'NETWORK_ERROR',
  TIMEOUT = 'TIMEOUT_ERROR'
}

// 标准化错误响应
export function createErrorResponse(
  code: string, 
  message: string, 
  details?: unknown,
  request_id?: string
): UnifiedErrorResponse {
  return {
    error: {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      request_id
    }
  }
}

// 根据状态码映射错误类型
function getErrorTypeFromStatus(status: number): ErrorType {
  switch (status) {
    case 401:
      return ErrorType.AUTHENTICATION
    case 403:
      return ErrorType.AUTHORIZATION
    case 400:
    case 422:
      return ErrorType.VALIDATION
    case 404:
      return ErrorType.NOT_FOUND
    case 500:
    case 502:
    case 503:
    case 504:
      return ErrorType.INTERNAL
    default:
      return ErrorType.INTERNAL
  }
}

// 处理 API 响应错误
export async function handleApiError(response: Response): Promise<UnifiedErrorResponse> {
  const status = response.status
  const errorType = getErrorTypeFromStatus(status)
  
  let errorDetails: unknown
  try {
    errorDetails = await response.json()
  } catch {
    errorDetails = response.statusText
  }

  // 从响应头获取请求 ID（用于追踪）
  const requestId = response.headers.get('X-Request-ID') || undefined

  // 根据错误类型提供用户友好的消息
  let userMessage: string
  switch (errorType) {
    case ErrorType.AUTHENTICATION:
      userMessage = '认证失败，请重新登录'
      break
    case ErrorType.AUTHORIZATION:
      userMessage = '您没有权限执行此操作'
      break
    case ErrorType.VALIDATION:
      userMessage = (errorDetails as { detail?: string })?.detail || '请求参数有误'
      break
    case ErrorType.NOT_FOUND:
      userMessage = '请求的资源不存在'
      break
    case ErrorType.NETWORK:
      userMessage = '网络连接错误，请检查网络设置'
      break
    case ErrorType.TIMEOUT:
      userMessage = '请求超时，请稍后重试'
      break
    default:
      userMessage = '服务器内部错误，请稍后重试'
  }

  return createErrorResponse(
    errorType,
    userMessage,
    errorDetails,
    requestId
  )
}

// 处理网络错误
export function handleNetworkError(error: Error): UnifiedErrorResponse {
  return createErrorResponse(
    ErrorType.NETWORK,
    '网络连接失败，请检查网络设置',
    error.message as unknown
  )
}

// 处理超时错误
export function handleTimeoutError(): UnifiedErrorResponse {
  return createErrorResponse(
    ErrorType.TIMEOUT,
    '请求超时，请稍后重试'
  )
}

// 检查是否为认证错误，需要重新登录
export function isAuthError(error: UnifiedErrorResponse): boolean {
  return error.error.code === ErrorType.AUTHENTICATION || 
         error.error.code === ErrorType.AUTHORIZATION
}

// 检查是否为网络相关错误，可以重试
export function isNetworkError(error: UnifiedErrorResponse): boolean {
  return error.error.code === ErrorType.NETWORK || 
         error.error.code === ErrorType.TIMEOUT
}

// 清除认证 token（内部使用）
function clearAuthTokens() {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }
}

// 错误处理 Hook 用于 React 组件
export function useErrorHandler() {
  const handleError = (error: UnifiedErrorResponse) => {
    console.error('API Error:', error)
    
    // 认证错误 - 重定向到登录页
    if (isAuthError(error)) {
      clearAuthTokens()
      
      // 如果不在登录页，则跳转
      if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
      return
    }
    
    // 网络错误 - 显示重试选项
    if (isNetworkError(error)) {
      // 可以在这里添加 toast 通知
      return
    }
    
    // 其他错误 - 显示错误消息
    // 可以在这里添加 toast 通知
  }
  
  return { handleError }
}

// 包装 fetch 函数，提供统一错误处理
export async function safeFetch(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  try {
    const response = await fetch(url, options)
    
    if (!response.ok) {
      const error = await handleApiError(response)
      throw error
    }
    
    return response
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw handleTimeoutError()
    }
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw handleNetworkError(error)
    }
    
    // 如果已经是我们的错误格式，直接抛出
    if (error && typeof error === 'object' && 'error' in error) {
      throw error
    }
    
    // 其他未知错误
    throw createErrorResponse(
      ErrorType.INTERNAL,
      '未知错误',
      error as unknown
    )
  }
}