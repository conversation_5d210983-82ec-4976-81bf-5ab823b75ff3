/**
 * Next.js API 代理中间件
 * 提供统一的认证转发、错误处理和日志记录
 */

import { NextRequest, NextResponse } from 'next/server'
import { handleApiError } from '@/utils/error-handler'
import { buildApiUrl } from '@/config/app-config'


// 生成请求 ID 用于追踪
function generateRequestId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

// 统一代理函数
export async function proxyRequest(
  request: NextRequest,
  backendPath: string,
  options: {
    method?: string
    body?: unknown
    contentType?: string
    accept?: string
  } = {}
): Promise<NextResponse> {
  const requestId = generateRequestId()
  const startTime = Date.now()
  
  try {
    // 获取客户端认证头
    const authHeader = request.headers.get('authorization')
    
    // 构建请求头
    const headers: Record<string, string> = {
      'Content-Type': options.contentType || 'application/json',
      'Accept': options.accept || 'application/json',
      'X-Request-ID': requestId,
      ...(authHeader ? { Authorization: authHeader } : {}),
    }
    
    // 构建请求体
    let requestBody: string | FormData | undefined
    if (options.body) {
      if (options.contentType?.includes('multipart/form-data')) {
        requestBody = options.body as FormData
      } else {
        requestBody = JSON.stringify(options.body)
      }
    }
    
    // 发送请求到后端
    const backendUrl = buildApiUrl(backendPath)
    const backendResponse = await fetch(backendUrl, {
      method: options.method || 'POST',
      headers,
      body: requestBody,
    })
    
    // 记录请求耗时
    const duration = Date.now() - startTime
    console.log(`[Proxy] ${request.method} ${backendPath} - ${backendResponse.status} (${duration}ms)`)
    
    // 处理错误响应
    if (!backendResponse.ok) {
      const error = await handleApiError(backendResponse)
      return NextResponse.json(error, { 
        status: backendResponse.status,
        headers: {
          'X-Request-ID': requestId,
          'Access-Control-Allow-Origin': '*',
        }
      })
    }
    
    // 处理成功响应
    const contentType = backendResponse.headers.get('content-type')
    
    if (contentType?.includes('application/json')) {
      const data = await backendResponse.json()
      return NextResponse.json(data, {
        status: backendResponse.status,
        headers: {
          'X-Request-ID': requestId,
          'Access-Control-Allow-Origin': '*',
        }
      })
    }
    
    if (contentType?.includes('text/event-stream')) {
      // 处理流式响应
      const stream = new ReadableStream({
        async start(controller) {
          const reader = backendResponse.body?.getReader()
          
          if (!reader) {
            controller.error(new Error('No response body'))
            return
          }
          
          try {
            const decoder = new TextDecoder()
            
            while (true) {
              const { done, value } = await reader.read()
              
              if (done) {
                controller.close()
                break
              }
              
              const chunk = decoder.decode(value, { stream: true })
              controller.enqueue(new TextEncoder().encode(chunk))
            }
          } catch (error) {
            console.error('Stream error:', error)
            controller.error(error)
          } finally {
            reader.releaseLock()
          }
        },
      })
      
      return new NextResponse(stream, {
        status: 200,
        headers: {
          'Content-Type': 'text/event-stream; charset=utf-8',
          'Cache-Control': 'no-cache, no-transform',
          'Connection': 'keep-alive',
          'Transfer-Encoding': 'chunked',
          'X-Request-ID': requestId,
          'Access-Control-Allow-Origin': '*',
        },
      })
    }
    
    // 处理其他类型的响应
    const data = await backendResponse.text()
    return new NextResponse(data, {
      status: backendResponse.status,
      headers: {
        'X-Request-ID': requestId,
        'Access-Control-Allow-Origin': '*',
      }
    })
    
  } catch (error) {
    console.error(`[Proxy] Error proxying ${backendPath}:`, error)
    
    return NextResponse.json({
      error: {
        code: 'PROXY_ERROR',
        message: '代理服务器错误',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        request_id: requestId
      }
    }, { 
      status: 500,
      headers: {
        'X-Request-ID': requestId,
        'Access-Control-Allow-Origin': '*',
      }
    })
  }
}

// 统一 CORS 处理
export function handleCORS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  })
}