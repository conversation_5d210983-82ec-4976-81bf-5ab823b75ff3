/**
 * 统一认证工具模块
 * 提供 token 管理、过期检测、自动刷新等功能
 */

import { buildApiUrl } from '@/config/app-config'

// JWT Token 解析接口
interface JWTPayload {
  sub: string
  role: string
  exp: number
  iat?: number
}

/**
 * 解析 JWT Token
 */
function parseJWT(token: string): JWTPayload | null {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('Failed to parse JWT:', error)
    return null
  }
}

/**
 * 检查 token 是否过期（提前 5 分钟判断为过期）
 */
function isTokenExpired(token: string): boolean {
  const payload = parseJWT(token)
  if (!payload || !payload.exp) {
    return true
  }
  
  const expirationTime = payload.exp * 1000 // Convert to milliseconds
  const currentTime = Date.now()
  const bufferTime = 5 * 60 * 1000 // 5 minutes buffer
  
  return currentTime >= (expirationTime - bufferTime)
}

/**
 * 获取认证头
 */
export function getAuthHeaders(): Record<string, string> {
  if (typeof window === 'undefined') {
    return {}
  }

  const accessToken = localStorage.getItem('access_token')
  if (!accessToken) {
    return {}
  }

  // 检查 token 是否过期
  if (isTokenExpired(accessToken)) {
    console.warn('Access token is expired, attempting refresh...')
    // 触发刷新流程（异步）
    refreshTokenIfNeeded().catch(error => {
      console.error('Token refresh failed:', error)
      // 如果刷新失败，清除 token 并可能重定向到登录页
      clearTokens()
      if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    })
    return {} // 暂时返回空，避免使用过期 token
  }

  return {
    Authorization: `Bearer ${accessToken}`
  }
}

/**
 * 自动刷新 token
 */
export async function refreshTokenIfNeeded(): Promise<boolean> {
  if (typeof window === 'undefined') {
    return false
  }

  const accessToken = localStorage.getItem('access_token')
  const refreshToken = localStorage.getItem('refresh_token')

  if (!refreshToken) {
    throw new Error('No refresh token available')
  }

  // 如果 access token 还没过期，不需要刷新
  if (accessToken && !isTokenExpired(accessToken)) {
    return true
  }

  try {
    const response = await fetch(buildApiUrl('/api/v1/auth/refresh'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${refreshToken}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`)
    }

    const data = await response.json()
    
    // 更新存储的 token
    localStorage.setItem('access_token', data.access_token)
    localStorage.setItem('refresh_token', data.refresh_token)
    
    console.log('Token refreshed successfully')
    return true
  } catch (error) {
    console.error('Token refresh failed:', error)
    clearTokens()
    throw error
  }
}

/**
 * 清除所有认证信息
 */
export function clearTokens(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }
}

/**
 * 检查是否已认证
 */
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') {
    return false
  }

  const accessToken = localStorage.getItem('access_token')
  if (!accessToken) {
    return false
  }

  return !isTokenExpired(accessToken)
}

/**
 * 带自动重试的 fetch 封装
 */
export async function authenticatedFetch(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  const headers = {
    'Content-Type': 'application/json',
    ...getAuthHeaders(),
    ...(options.headers || {})
  }

  let response = await fetch(url, {
    ...options,
    headers
  })

  // 如果返回 401，尝试刷新 token 并重试一次
  if (response.status === 401) {
    try {
      await refreshTokenIfNeeded()
      
      // 重新获取认证头并重试
      const newHeaders = {
        'Content-Type': 'application/json',
        ...getAuthHeaders(),
        ...(options.headers || {})
      }
      
      response = await fetch(url, {
        ...options,
        headers: newHeaders
      })
    } catch (error) {
      console.error('Token refresh and retry failed:', error)
      // 如果重试仍然失败，可能需要重新登录
      if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
      throw error
    }
  }

  return response
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser(): JWTPayload | null {
  if (typeof window === 'undefined') {
    return null
  }

  const accessToken = localStorage.getItem('access_token')
  if (!accessToken || isTokenExpired(accessToken)) {
    return null
  }

  return parseJWT(accessToken)
}