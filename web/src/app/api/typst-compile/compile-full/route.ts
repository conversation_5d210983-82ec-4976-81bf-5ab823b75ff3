import { NextRequest, NextResponse } from 'next/server'
import { buildApiUrl } from '@/config/app-config'


export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    const body = await request.json()
    
    // 验证请求体
    if (!body.markdown || typeof body.markdown !== 'string') {
      return NextResponse.json(
        { error: 'Markdown content is required and must be a string' },
        { status: 400 }
      )
    }

    const backendResponse = await fetch(buildApiUrl('/api/typst-compile/compile-full'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(authHeader ? { Authorization: authHeader } : {}),
      },
      body: JSON.stringify(body),
    })

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      return NextResponse.json(
        { 
          error: `Backend error: ${backendResponse.status} ${backendResponse.statusText}`,
          details: errorText 
        },
        { status: backendResponse.status }
      )
    }

    // 获取响应数据（应该是二进制向量文件）
    const vectorData = await backendResponse.arrayBuffer()
    
    // 返回二进制数据
    return new NextResponse(vectorData, {
      status: 200,
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': 'attachment; filename=compiled.artifact.sir.in',
        'Cache-Control': 'no-cache',
      },
    })

  } catch (error) {
    console.error('Typst compile proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
