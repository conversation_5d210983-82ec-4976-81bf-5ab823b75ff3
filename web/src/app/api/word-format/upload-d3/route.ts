import { NextRequest, NextResponse } from 'next/server'
import { getAccessTokenFromRequest, buildAuthHeader } from '@/lib/server-auth'
import { buildApiUrl } from '@/config/app-config'


export async function POST(request: NextRequest) {
  try {
    const token = getAccessTokenFromRequest(request)
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    const backendFormData = new FormData()
    backendFormData.append('file', file)

    const backendResponse = await fetch(buildApiUrl('/api/word-format/upload-d3'), {
      method: 'POST',
      body: backendFormData,
      headers: {
        ...buildAuthHeader(token),
      },
    })

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      return NextResponse.json(
        { 
          error: `Backend error: ${backendResponse.status} ${backendResponse.statusText}`,
          details: errorText 
        },
        { status: backendResponse.status }
      )
    }

    const data = await backendResponse.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Internal proxy error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
