import { NextRequest, NextResponse } from 'next/server'
import { getAccessTokenFromRequest, buildAuthHeader } from '@/lib/server-auth'
import { buildApiUrl } from '@/config/app-config'


export async function POST(request: NextRequest) {
  try {
    const token = getAccessTokenFromRequest(request)
    const body = await request.json()
    
    if (!body.markdown) {
      return NextResponse.json(
        { error: 'Markdown content is required' },
        { status: 400 }
      )
    }

    const backendResponse = await fetch(buildApiUrl('/api/word-format/export-pdf'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...buildAuthHeader(token),
      },
      body: JSON.stringify({ markdown: body.markdown }),
    })

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      return NextResponse.json(
        { 
          error: `Backend error: ${backendResponse.status} ${backendResponse.statusText}`,
          details: errorText 
        },
        { status: backendResponse.status }
      )
    }

    const blob = await backendResponse.blob()
    return new NextResponse(blob, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="export.pdf"',
      },
    })
  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Internal proxy error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
