import { NextRequest, NextResponse } from 'next/server'
import { getAccessTokenFromRequest, buildAuthHeader } from '@/lib/server-auth'
import { buildApiUrl } from '@/config/app-config'


export async function GET(
  request: NextRequest,
  context: { params: Promise<{ docId: string }> }
) {
  const { docId } = await context.params
  try {
    const token = getAccessTokenFromRequest(request)
    
    const backendResponse = await fetch(buildApiUrl(`/api/word-format/documents/${docId}`), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...buildAuthHeader(token),
      },
    })

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      return NextResponse.json(
        { 
          error: `Backend error: ${backendResponse.status} ${backendResponse.statusText}`,
          details: errorText 
        },
        { status: backendResponse.status }
      )
    }

    const data = await backendResponse.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Internal proxy error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ docId: string }> }
) {
  const { docId } = await context.params
  try {
    const token = getAccessTokenFromRequest(request)
    
    const backendResponse = await fetch(buildApiUrl(`/api/word-format/documents/${docId}`), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...buildAuthHeader(token),
      },
    })

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      return NextResponse.json(
        { 
          error: `Backend error: ${backendResponse.status} ${backendResponse.statusText}`,
          details: errorText 
        },
        { status: backendResponse.status }
      )
    }

    const data = await backendResponse.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Internal proxy error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
