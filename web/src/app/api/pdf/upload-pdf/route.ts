import { NextRequest, NextResponse } from 'next/server'
import { getAccessTokenFromRequest, buildAuthHeader } from '@/lib/server-auth'
import { buildApiUrl } from '@/config/app-config'

/**
 * PDF上传代理路由
 * 将前端PDF上传请求代理到后端FastAPI服务
 */
export async function POST(request: NextRequest) {
  try {
    // 获取访问令牌用于认证
    const token = getAccessTokenFromRequest(request)
    
    // 解析表单数据获取上传的文件
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // 构建发送到后端的表单数据
    const backendFormData = new FormData()
    backendFormData.append('file', file)

    // 转发请求到后端API
    const backendResponse = await fetch(buildApiUrl('/api/pdf/upload-pdf'), {
      method: 'POST',
      body: backendFormData,
      headers: {
        ...buildAuthHeader(token),
      },
    })

    // 处理后端响应
    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      return NextResponse.json(
        {
          error: `Backend error: ${backendResponse.status} ${backendResponse.statusText}`,
          details: errorText
        },
        { status: backendResponse.status }
      )
    }

    // 成功响应
    const data = await backendResponse.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('PDF upload proxy error:', error)
    return NextResponse.json(
      {
        error: 'Internal proxy error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}