import type { Metadata } from 'next'
import { Toaster } from 'sonner'
import '../styles/globals.css'
import '../components/ai-editor/ai-editor.css'


export const metadata: Metadata = {
  title: '智能AI报告编辑器',
  description: '独立的AI编辑器组件，基于Novel + TipTap + ProseMirror技术栈',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body suppressHydrationWarning>
        {children}
        <Toaster richColors position="top-right" />
      </body>
    </html>
  )
}
