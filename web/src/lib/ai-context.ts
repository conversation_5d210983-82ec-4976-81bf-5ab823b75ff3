/**
 * AI Context Helper
 * Provides unified context-aware prompt building for all AI interactions
 */

export interface ContextAwarePromptOptions {
  userPrompt: string
  selectedText?: string
  fullText?: string
  maxContextLength?: number
  maxSelectedLength?: number
}

/**
 * Build context-aware AI prompt with document understanding
 * 
 * This function implements the KISS principle by centralizing prompt logic
 * and avoiding duplication between different AI components.
 */
export function buildContextAwarePrompt({
  userPrompt,
  selectedText = '',
  fullText = '',
  maxContextLength = 1500,
  maxSelectedLength = 5000
}: ContextAwarePromptOptions): {
  prompt: string
  context: string
  selected_text: string
} {
  // Determine the working text (selected text takes priority)
  const hasSelection = selectedText?.trim()?.length > 0
  const workingText = hasSelection ? selectedText : ''
  
  // Process text length limits
  const processedSelected = workingText.length > maxSelectedLength 
    ? workingText.substring(0, maxSelectedLength) 
    : workingText
    
  const contextText = fullText && fullText.length > 100 
    ? fullText.substring(0, maxContextLength) 
    : ''

  // Build the complete prompt with context awareness
  let finalPrompt = userPrompt

  // Add context if available
  if (contextText) {
    finalPrompt = `${userPrompt}

当前文档上下文参考：
${contextText}...

请基于当前文档的格式、风格和内容结构来处理以下内容：${processedSelected ? `\n\n待处理内容：\n${processedSelected}` : ''}`
  } else if (processedSelected) {
    // Fallback to simple format if no context available
    finalPrompt = `${userPrompt}：\n${processedSelected}`
  }

  return {
    prompt: finalPrompt,
    context: fullText,
    selected_text: processedSelected
  }
}

/**
 * Create optimized AI request from user input
 * 
 * Follows DRY principle by providing a single function for all AI request creation
 */
export function createAIRequest(
  userPrompt: string,
  selectedText?: string,
  fullText?: string
): {
  prompt: string
  context: string
  selected_text: string
} {
  return buildContextAwarePrompt({
    userPrompt,
    selectedText,
    fullText
  })
}