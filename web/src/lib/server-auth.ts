import { NextRequest, NextResponse } from 'next/server'
import { cookies as nextCookies } from 'next/headers'

const isProd = process.env.NODE_ENV === 'production'

export function getAccessTokenFromRequest(req: NextRequest): string | null {
  try {
    const token = req.cookies.get('access_token')?.value
    return token || null
  } catch {
    return null
  }
}

export function buildAuthHeader(token: string | null): Record<string, string> {
  return token ? { Authorization: `Bearer ${token}` } : {}
}

export function setAuthCookies(
  res: NextResponse,
  accessToken: string,
  refreshToken: string,
  accessTokenExpiresInSeconds?: number,
) {
  const cookieBase = {
    httpOnly: true as const,
    sameSite: 'lax' as const,
    secure: isProd,
    path: '/',
  }

  // Access token (short-lived)
  res.cookies.set('access_token', accessToken, {
    ...cookieBase,
    maxAge: accessTokenExpiresInSeconds ?? 30 * 60, // default 30 minutes
  })

  // Refresh token (longer-lived)
  res.cookies.set('refresh_token', refreshToken, {
    ...cookieBase,
    maxAge: 7 * 24 * 60 * 60, // default 7 days
  })
}

export function clearAuthCookies(res: NextResponse) {
  const cookieBase = {
    httpOnly: true as const,
    sameSite: 'lax' as const,
    secure: isProd,
    path: '/',
  }
  res.cookies.set('access_token', '', { ...cookieBase, maxAge: 0 })
  res.cookies.set('refresh_token', '', { ...cookieBase, maxAge: 0 })
}

// Helper for handlers that are not passed NextRequest
export async function getAccessTokenFromServerCookies(): Promise<string | null> {
  try {
    const jar = await nextCookies()
    return jar.get('access_token')?.value || null
  } catch {
    return null
  }
}

