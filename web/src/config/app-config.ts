/**
 * 统一应用配置管理
 * 提供类型安全的环境变量访问和配置管理
 * 消除重复的环境变量读取代码
 */

interface AppConfig {
  // 后端服务配置
  backend: {
    apiUrl: string
    websocketUrl: string
    timeout: number
  }
}

// 默认配置
const DEFAULT_CONFIG = {
  backend: {
    apiUrl: 'http://localhost:8000',
    websocketUrl: 'ws://localhost:8000',
    timeout: 300000,
  }
} as const


/**
 * 获取应用配置
 * 开发环境下不缓存，生产环境缓存配置
 */
let cachedConfig: AppConfig | null = null

export function getAppConfig(): AppConfig {
  // 开发环境下不缓存配置，确保环境变量变更能立即生效
  const isDev = process.env.NODE_ENV === 'development'
  
  if (!isDev && cachedConfig) {
    return cachedConfig
  }

  const config: AppConfig = {
    backend: {
      apiUrl: process.env.NEXT_PUBLIC_AI_BACKEND_URL || DEFAULT_CONFIG.backend.apiUrl,
      websocketUrl: process.env.NEXT_PUBLIC_WEBSOCKET_URL || DEFAULT_CONFIG.backend.websocketUrl,
      timeout: parseInt(process.env.NEXT_PUBLIC_AI_TIMEOUT || '0') || DEFAULT_CONFIG.backend.timeout,
    }
  }

  if (!isDev) {
    cachedConfig = config
  }
  return config
}

/**
 * 获取后端API URL
 * 用于替换分散在各处的 process.env.NEXT_PUBLIC_AI_BACKEND_URL
 */
export function getBackendUrl(): string {
  return getAppConfig().backend.apiUrl
}

/**
 * 获取WebSocket URL
 * 用于WebSocket连接
 */
export function getWebSocketUrl(): string {
  return getAppConfig().backend.websocketUrl
}

/**
 * 构建完整的API端点URL
 */
export function buildApiUrl(path: string, baseUrl?: string): string {
  const base = baseUrl || getBackendUrl()
  const cleanBaseUrl = base.replace(/\/$/, '')
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${cleanBaseUrl}${cleanPath}`
}

/**
 * 构建WebSocket连接URL
 */
export function buildWebSocketUrl(path: string, sessionId?: string): string {
  const baseUrl = getWebSocketUrl().replace(/\/$/, '')
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  let wsUrl = `${baseUrl}${cleanPath}`
  
  if (sessionId) {
    wsUrl += `/${sessionId}`
  }
  
  // 附带认证token
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('access_token')
    if (token) {
      wsUrl += (wsUrl.includes('?') ? '&' : '?') + `token=${encodeURIComponent(token)}`
    }
  }
  
  return wsUrl
}

/**
 * 获取超时时间
 */
export function getTimeout(): number {
  return getAppConfig().backend.timeout
}

