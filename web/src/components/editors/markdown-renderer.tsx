"use client"

import { useState, useEffect } from 'react'
import DOMPurify from 'dompurify'
import { apiClient } from '@/lib/api-client'
import { Chart, Table } from '@/types/chart'
import { cn } from '@/lib/utils'

interface MarkdownRendererProps {
  markdown: string
  charts: Chart[]
  tables: Table[]
}

export function MarkdownRenderer({ markdown, charts, tables }: MarkdownRendererProps) {
  const [processedContent, setProcessedContent] = useState<string>(markdown)
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    const processMarkdown = async () => {
      setIsProcessing(true)
      let processedMarkdown = markdown

      try {
        // 处理图表占位符 [CHART:chart_id] 
        const chartPlaceholders = processedMarkdown.match(/\[CHART:[^\]]+\]/g) || []
        
        for (const placeholder of chartPlaceholders) {
          const chartId = placeholder.match(/\[CHART:([^\]]+)\]/)?.[1]
          if (chartId) {
            // 根据ID查找对应的图表配置
            const chart = charts.find(c => c.chartId === chartId || c.id === chartId)
            if (chart) {
              try {
                // 生成SVG
                const svgContent = await apiClient.generateChartSVG(chart)
                // 安全过滤SVG内容
                const sanitizedSVG = DOMPurify.sanitize(svgContent, {
                  ALLOWED_TAGS: ['svg', 'path', 'circle', 'rect', 'line', 'text', 'g', 'defs', 'pattern'],
                  ALLOWED_ATTR: ['width', 'height', 'viewBox', 'd', 'fill', 'stroke', 'stroke-width', 'cx', 'cy', 'r', 'x', 'y', 'font-size', 'font-weight', 'text-anchor', 'class', 'id']
                })
                // 替换占位符为安全的SVG
                const chartContainerClass = cn("chart-container my-4")
                processedMarkdown = processedMarkdown.replace(
                  placeholder, 
                  `<div class="${chartContainerClass}">${sanitizedSVG}</div>`
                )
              } catch (error) {
                console.error('Failed to generate chart SVG:', error)
                // 替换为错误信息
                const chartErrorClass = cn("chart-error p-4 border border-red-200 rounded-lg bg-red-50")
                const textRedClass = cn("text-red-600")
                processedMarkdown = processedMarkdown.replace(
                  placeholder,
                  `<div class="${chartErrorClass}">
                    <p class="${textRedClass}">图表加载失败: ${DOMPurify.sanitize(chartId)}</p>
                  </div>`
                )
              }
            } else {
              // 找不到对应图表配置
              const chartNotFoundClass = cn("chart-error p-4 border border-yellow-200 rounded-lg bg-yellow-50")
              const textYellowClass = cn("text-yellow-600")
              processedMarkdown = processedMarkdown.replace(
                placeholder,
                `<div class="${chartNotFoundClass}">
                  <p class="${textYellowClass}">未找到图表: ${DOMPurify.sanitize(chartId)}</p>
                </div>`
              )
            }
          }
        }

        // 处理表格占位符 [TABLE:table_id]
        const tablePlaceholders = processedMarkdown.match(/\[TABLE:[^\]]+\]/g) || []
        
        for (const placeholder of tablePlaceholders) {
          const tableId = placeholder.match(/\[TABLE:([^\]]+)\]/)?.[1]
          if (tableId) {
            // 根据ID查找对应的表格配置
            const table = tables.find(t => t.id === tableId)
            if (table) {
              // 生成HTML表格
              const tableHTML = generateTableHTML(table)
              // 替换占位符
              processedMarkdown = processedMarkdown.replace(placeholder, tableHTML)
            } else {
              // 找不到对应表格
              const tableErrorClass = cn("table-error p-4 border border-yellow-200 rounded-lg bg-yellow-50")
              const textYellowClass = cn("text-yellow-600")
              processedMarkdown = processedMarkdown.replace(
                placeholder,
                `<div class="${tableErrorClass}">
                  <p class="${textYellowClass}">未找到表格: ${DOMPurify.sanitize(tableId)}</p>
                </div>`
              )
            }
          }
        }

        setProcessedContent(processedMarkdown)
      } catch (error) {
        console.error('Error processing markdown:', error)
        setProcessedContent(markdown)
      } finally {
        setIsProcessing(false)
      }
    }

    processMarkdown()
  }, [markdown, charts, tables])

  // 安全过滤最终的HTML内容
  const sanitizedContent = DOMPurify.sanitize(processedContent, {
    ALLOWED_TAGS: ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'br', 'svg', 'path', 'circle', 'rect', 'line', 'text', 'g', 'table', 'thead', 'tbody', 'tr', 'th', 'td'],
    ALLOWED_ATTR: ['class', 'id', 'href', 'target', 'width', 'height', 'viewBox', 'd', 'fill', 'stroke', 'stroke-width', 'cx', 'cy', 'r', 'x', 'y', 'font-size', 'font-weight', 'text-anchor']
  })

  if (isProcessing) {
    return (
      <div className={cn("prose prose-lg max-w-none dark:prose-invert")}>
        <div className={cn("flex items-center justify-center p-4 text-gray-500")}>
          正在处理内容...
        </div>
      </div>
    )
  }

  return (
    <div className={cn("prose prose-lg max-w-none dark:prose-invert")}>
      <div 
        dangerouslySetInnerHTML={{ __html: sanitizedContent }}
      />
    </div>
  )
}

// 生成表格HTML的辅助函数
function generateTableHTML(table: Table): string {
  const { headers, rows, title } = table
  
  const tableContainerClass = cn("table-container my-4")
  const titleClass = cn("text-lg font-semibold mb-2")
  const overflowClass = cn("overflow-x-auto border rounded-lg")
  const tableClass = cn("min-w-full bg-white")
  const theadClass = cn("bg-gray-50")
  const thClass = cn("px-4 py-2 text-left text-sm font-medium text-gray-700 border-b")
  const tdClass = cn("px-4 py-2 text-sm text-gray-900 border-b")
  
  let html = `<div class="${tableContainerClass}">`
  
  if (title) {
    html += `<h3 class="${titleClass}">${DOMPurify.sanitize(title)}</h3>`
  }
  
  html += `<div class="${overflowClass}">`
  html += `<table class="${tableClass}">`
  
  // 表头
  if (headers && headers.length > 0) {
    html += `<thead class="${theadClass}">`
    html += '<tr>'
    headers.forEach((header: string) => {
      html += `<th class="${thClass}">${DOMPurify.sanitize(header)}</th>`
    })
    html += '</tr>'
    html += '</thead>'
  }
  
  // 表体
  html += '<tbody>'
  if (rows && rows.length > 0) {
    rows.forEach((row: string[], index: number) => {
      const bgClass = cn(index % 2 === 0 ? 'bg-white' : 'bg-gray-50')
      html += `<tr class="${bgClass}">`
      row.forEach((cell: string) => {
        html += `<td class="${tdClass}">${DOMPurify.sanitize(cell)}</td>`
      })
      html += '</tr>'
    })
  }
  html += '</tbody>'
  
  html += '</table>'
  html += '</div>'
  html += '</div>'
  
  return html
}