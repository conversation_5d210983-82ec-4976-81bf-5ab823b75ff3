"use client"

import { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, Send, Loader2, Copy } from 'lucide-react'
import { QUICK_AI_PROMPTS } from '@/lib/ai-commands'
import { useAI } from '@/hooks/use-ai'
import { cn } from '@/lib/utils'

interface AIPanelProps {
  selectedText?: string
  fullText?: string
  onInsert: (text: string) => void
  onReplace: (text: string) => void
}

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

export function AIPanel({ selectedText, fullText, onInsert, onReplace }: AIPanelProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  
  const { complete, isLoading: aiLoading, completion } = useAI({
    onError: (error) => {
      console.error('AI错误:', error)
    }
  })

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, completion])

  const addMessage = (role: 'user' | 'assistant', content: string) => {
    const message: Message = {
      id: Date.now().toString(),
      role,
      content,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, message])
  }
  
  // 是否存在真实的选中文本（去除空白后判断）
  const hasRealSelection = ((selectedText?.trim()?.length ?? 0) > 0)

  const handleQuickPrompt = async (promptText: string) => {
    setIsProcessing(true)
    
    // 快速操作消息中只显示操作名称，不显示选中文本
    addMessage('user', promptText)
    
    try {
      const result = await complete({
        prompt: promptText,
        context: fullText || '',
        selected_text: selectedText || ''
      })
      if (result) {
        addMessage('assistant', result)
      }
    } catch (error) {
      console.error('AI处理失败:', error)
      addMessage('assistant', '抱歉，AI处理出现错误，请重试。')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSendMessage = async () => {
    if (!input.trim() || isProcessing) return

    const userMessage = input.trim()
    setInput('')
    setIsProcessing(true)
    
    addMessage('user', userMessage)

    try {
      const result = await complete({
        prompt: userMessage,
        context: fullText || '',
        selected_text: selectedText || ''
      })
      if (result) {
        addMessage('assistant', result)
      }
    } catch (error) {
      console.error('AI处理失败:', error)
      addMessage('assistant', '抱歉，AI处理出现错误，请重试。')
    } finally {
      setIsProcessing(false)
    }
  }


  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
    }
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [input])

  return (
    <div className={cn("h-full flex flex-col bg-white dark:bg-gray-900")}>
      {/* 标题 */}
      <div className={cn("p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0")}>
        <h3 className={cn("text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center")}>
          <Sparkles className={cn("h-5 w-5 mr-2 text-blue-500")} />
          AI助手
        </h3>
      </div>

      {/* 快速操作区域 */}
      <div className={cn("p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0")}>
        <h4 className={cn("text-sm font-medium text-gray-700 dark:text-gray-300 mb-2")}>快速操作</h4>
        
        
        <div className={cn("grid grid-cols-2 gap-2")}>
          {QUICK_AI_PROMPTS.slice(0, 4).map((prompt, index) => (
            <button
              key={index}
              onClick={() => handleQuickPrompt(prompt.prompt)}
              disabled={isProcessing}
              className={cn(
                "flex items-center justify-center p-2 text-xs border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              )}
            >
              <prompt.icon className={cn("h-3 w-3 mr-1")} />
              <span>{prompt.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 当前处理文本显示 */}
      {(selectedText || fullText) && (
        <div className={cn("p-4 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800 flex-shrink-0")}>
          <div className={cn("flex items-center justify-between mb-2")}>
            <span className={cn("text-xs font-medium text-blue-700 dark:text-blue-300")}>
              {hasRealSelection ? '选中的文本' : '当前全文'} ({(selectedText || fullText || '').length}字符)
            </span>
            <button
              onClick={() => copyToClipboard(selectedText || fullText || '')}
              className={cn("p-1 hover:bg-blue-100 dark:hover:bg-blue-800 rounded")}
            >
              <Copy className={cn("h-3 w-3 text-blue-600 dark:text-blue-400")} />
            </button>
          </div>
          <p className={cn("text-sm text-blue-800 dark:text-blue-200 line-clamp-3")}>
            {hasRealSelection ? selectedText : fullText}
          </p>
        </div>
      )}

      {/* 消息列表 */}
      <div className={cn("flex-1 overflow-y-auto p-4 space-y-4")}>
        {messages.length === 0 ? (
          <div className={cn("text-center text-gray-500 dark:text-gray-400 py-8")}>
            <Sparkles className={cn("h-8 w-8 mx-auto mb-2 opacity-50")} />
            <p className={cn("text-sm")}>开始与AI对话</p>
            <p className={cn("text-xs mt-1")}>选择文本后使用快速操作，或直接输入消息</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className={cn("flex", message.role === 'user' ? 'justify-end' : 'justify-start')}>
              <div className={cn(
                "max-w-[80%] rounded-lg p-3",
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
              )}>
                <p className={cn("text-sm whitespace-pre-wrap")}>{message.content}</p>
                <div className={cn("flex items-center justify-between mt-2")}>
                  <span className={cn(
                    "text-xs opacity-70",
                    message.role === 'user' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                  )}>
                    {formatTime(message.timestamp)}
                  </span>
                  
                  {message.role === 'assistant' && (
                    <div className={cn("flex items-center space-x-1")}>
                      <button
                        onClick={() => copyToClipboard(message.content)}
                        className={cn("p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded")}
                      >
                        <Copy className={cn("h-3 w-3")} />
                      </button>
                      <button
                        onClick={() => (hasRealSelection ? onReplace(message.content) : onInsert(message.content))}
                        className={cn("px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700")}
                      >
                        {hasRealSelection ? '替换' : '插入'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
        
        {/* AI正在思考中的提示 */}
        {(isProcessing || aiLoading) && (
          <div className={cn("flex justify-start")}>
            <div className={cn("bg-gray-100 dark:bg-gray-700 rounded-lg p-3 flex items-center")}>
              <Loader2 className={cn("h-4 w-4 animate-spin mr-2 text-blue-500")} />
              <span className={cn("text-sm text-gray-600 dark:text-gray-400")}>AI正在思考...</span>
            </div>
          </div>
        )}
        
        {/* 流式响应显示 */}
        {completion && aiLoading && (
          <div className={cn("flex justify-start")}>
            <div className={cn("max-w-[80%] bg-gray-100 dark:bg-gray-700 rounded-lg p-3")}>
              <p className={cn("text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap")}>
                {completion}
                <span className={cn("inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1")} />
              </p>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className={cn("p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0")}>
        <div className={cn("flex items-end space-x-2")}>
          <div className={cn("flex-1")}>
            <textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入消息，Enter发送，Shift+Enter换行"
              className={cn(
                "w-full resize-none rounded-md border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              )}
              style={{ minHeight: '38px', maxHeight: '120px' }}
              rows={1}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!input.trim() || isProcessing}
            className={cn(
              "p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
            )}
          >
            {isProcessing ? (
              <Loader2 className={cn("h-4 w-4 animate-spin")} />
            ) : (
              <Send className={cn("h-4 w-4")} />
            )}
          </button>
        </div>
      </div>
    </div>
  )
}