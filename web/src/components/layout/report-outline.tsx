"use client"

import { useState, useEffect } from 'react'
import { FileText, ChevronRight, ChevronDown, Hash } from 'lucide-react'

interface OutlineItem {
  id: string
  text: string
  level: number
  position: number
}

interface ReportOutlineProps {
  markdownContent: string
  onNavigate: (position: number) => void
}

export function ReportOutline({ markdownContent, onNavigate }: ReportOutlineProps) {
  const [outline, setOutline] = useState<OutlineItem[]>([])
  const [collapsed, setCollapsed] = useState<Set<string>>(new Set())

  useEffect(() => {
    const generateOutline = () => {
      const lines = markdownContent.split('\n')
      const items: OutlineItem[] = []
      
      lines.forEach((line, index) => {
        const match = line.match(/^(#{1,6})\s+(.+)$/)
        if (match) {
          const level = match[1].length
          const text = match[2].trim()
          items.push({
            id: `heading-${index}`,
            text,
            level,
            position: index
          })
        }
      })
      
      setOutline(items)
    }

    generateOutline()
  }, [markdownContent])

  const toggleCollapse = (id: string) => {
    const newCollapsed = new Set(collapsed)
    if (newCollapsed.has(id)) {
      newCollapsed.delete(id)
    } else {
      newCollapsed.add(id)
    }
    setCollapsed(newCollapsed)
  }

  const shouldShowItem = (item: OutlineItem, index: number) => {
    // Always show level 1 headings
    if (item.level === 1) return true
    
    // Find the parent heading
    for (let i = index - 1; i >= 0; i--) {
      const parent = outline[i]
      if (parent.level < item.level) {
        return !collapsed.has(parent.id)
      }
    }
    
    return true
  }

  if (outline.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
        <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">暂无大纲内容</p>
        <p className="text-xs mt-1">开始编辑添加标题</p>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-1">
      <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
        <FileText className="h-4 w-4 mr-2" />
        文档大纲
      </h3>
      
      <div className="space-y-1">
        {outline.map((item, index) => {
          if (!shouldShowItem(item, index)) return null
          
          const hasChildren = index < outline.length - 1 && outline[index + 1].level > item.level
          const isCollapsed = collapsed.has(item.id)
          
          return (
            <div key={item.id}>
              <div
                className="outline-item group flex items-center px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                style={{ paddingLeft: `${8 + (item.level - 1) * 12}px` }}
                onClick={() => onNavigate(item.position)}
              >
                {hasChildren && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleCollapse(item.id)
                    }}
                    className="mr-1 p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                  >
                    {isCollapsed ? (
                      <ChevronRight className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    )}
                  </button>
                )}
                
                {!hasChildren && (
                  <div className="w-4 mr-1 flex justify-center">
                    <Hash className="h-3 w-3 text-gray-400" />
                  </div>
                )}
                
                <span
                  className="text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 truncate"
                  style={{
                    fontSize: `${Math.max(12, 14 - (item.level - 1))}px`,
                    fontWeight: item.level === 1 ? 600 : item.level === 2 ? 500 : 400
                  }}
                >
                  {item.text}
                </span>
              </div>
            </div>
          )
        })}
      </div>
      
      {outline.length > 0 && (
        <div className="pt-3 mt-3 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            共 {outline.length} 个标题
          </p>
        </div>
      )}
    </div>
  )
}