import type { CompilationProgress } from '@/hooks/useRealtimeCompilation'

// TODO(next): 当后端实现 diff 分析/增量编译阶段时，扩展阶段枚举并在此添加映射
const STAGE_LABELS: Record<NonNullable<CompilationProgress['stage']>, string> = {
  markdown_to_typst: 'Markdown → Typst',
  typst_to_vector: 'Typst → Vector',
  vector_generation: '向量生成',
}

export function formatProgress(progress: CompilationProgress | null): string {
  if (!progress) return ''
  const stage = STAGE_LABELS[progress.stage] ?? progress.stage
  const pct = Math.round((progress.progress ?? 0) * 100)
  return `${stage} ${pct}%`
}
