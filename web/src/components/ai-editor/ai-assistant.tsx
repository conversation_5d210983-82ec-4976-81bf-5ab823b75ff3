"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  Check,
  Copy,
  Loader2,
  Plus,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { QUICK_AI_PROMPTS } from "@/lib/ai-commands"

interface AIAssistantProps {
  selectedText: string
  suggestion: string
  isLoading: boolean
  onGenerate: (prompt: string, context?: string) => Promise<void>
  onInsert: (text: string) => void
  onReplace: (text: string) => void
  onClose: () => void
  fullText?: string
}

// 使用统一的AI命令定义

export function AIAssistant({
  selectedText,
  suggestion,
  isLoading,
  onGenerate,
  onInsert,
  onReplace,
  onClose,
  fullText,
}: AIAssistantProps) {
  const [customPrompt, setCustomPrompt] = useState("")
  const [showCustomInput, setShowCustomInput] = useState(false)
  const suggestionRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when suggestion updates during streaming
  useEffect(() => {
    if (suggestionRef.current && isLoading && suggestion) {
      suggestionRef.current.scrollTop = suggestionRef.current.scrollHeight
    }
  }, [suggestion, isLoading])

  // 获取当前工作文本（选中文本优先，否则全文）
  const getCurrentText = (): string => {
    if (selectedText?.trim()?.length > 0) {
      return selectedText
    }
    return fullText || ''
  }

  
  const handleQuickPrompt = async (prompt: string) => {
    // 使用简化的prompt，不包含选中文本
    await onGenerate(prompt, getCurrentText())
  }

  const handleCustomPrompt = async () => {
    if (customPrompt.trim()) {
      // 使用简化的prompt，不包含选中文本
      await onGenerate(customPrompt, getCurrentText())
      setCustomPrompt("")
      setShowCustomInput(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (error) {
      console.error("复制失败:", error)
    }
  }

  return (
    <motion.div
      className={cn(
        "fixed inset-0 bg-black/20 backdrop-blur-xs z-50 flex items-center justify-center p-4"
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className={cn(
          "w-full max-w-2xl max-h-[80vh] bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 flex flex-col"
        )}
        initial={{ scale: 0.95, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.95, opacity: 0, y: 20 }}
        transition={{ duration: 0.2 }}
      >
        <div className={cn(
          "flex flex-row items-center justify-between space-y-0 p-6 pb-4 flex-shrink-0"
        )}>
          <div>
            <h3 className={cn(
              "flex items-center space-x-2 text-lg font-semibold"
            )}>
              <Sparkles className={cn("h-5 w-5 text-blue-500")} />
              <span>AI 写作助手</span>
            </h3>
            <p className={cn(
              "text-sm text-gray-600 dark:text-gray-400 mt-1"
            )}>
              {selectedText ? "为选中的文字提供 AI 建议" : "使用 AI 生成内容"}
            </p>
          </div>
          <button 
            onClick={onClose}
            className={cn(
              "p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            )}
          >
            <X className={cn("h-4 w-4")} />
          </button>
        </div>

        <div className={cn(
          "px-6 pb-6 space-y-4 overflow-y-auto flex-1"
        )}>
          {/* 选中的文字 */}
          {selectedText && (
            <div className={cn(
              "p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            )}>
              <div className={cn(
                "flex items-center justify-between mb-2"
              )}>
                <span className={cn(
                  "text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded"
                )}>选中的文字</span>
                <button
                  onClick={() => copyToClipboard(selectedText)}
                  className={cn(
                    "p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                  )}
                >
                  <Copy className={cn("h-3 w-3")} />
                </button>
              </div>
              <p className={cn(
                "text-sm text-gray-700 dark:text-gray-300"
              )}>
                {selectedText}
              </p>
            </div>
          )}

          {/* 快速提示词 */}
          <div className={cn(
            "space-y-2"
          )}>
            <h4 className={cn(
              "text-sm font-medium"
            )}>快速操作</h4>
            <div className={cn(
              "grid grid-cols-2 gap-2"
            )}>
              {QUICK_AI_PROMPTS.map((prompt, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickPrompt(prompt.prompt)}
                  disabled={isLoading || !selectedText}
                  className={cn(
                    "flex items-center justify-start p-2 text-sm border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  )}
                >
                  <prompt.icon className={cn("h-4 w-4")} />
                  <span className={cn("ml-2")}>{prompt.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* 自定义提示词 */}
          <div className={cn(
            "space-y-2"
          )}>
            <div className={cn(
              "flex items-center justify-between"
            )}>
              <h4 className={cn(
                "text-sm font-medium"
              )}>自定义指令</h4>
              <button
                onClick={() => setShowCustomInput(!showCustomInput)}
                className={cn(
                  "text-sm text-blue-600 dark:text-blue-400 hover:underline"
                )}
              >
                {showCustomInput ? "收起" : "展开"}
              </button>
            </div>
            
            {showCustomInput && (
              <div className={cn(
                "space-y-2"
              )}>
                <textarea
                  placeholder="输入你的自定义指令..."
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  className={cn(
                    "w-full min-h-[80px] p-2 border border-gray-200 dark:border-gray-700 rounded resize-none bg-white dark:bg-gray-800 text-sm"
                  )}
                />
                <button
                  onClick={handleCustomPrompt}
                  disabled={isLoading || !customPrompt.trim()}
                  className={cn(
                    "w-full flex items-center justify-center p-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  )}
                >
                  {isLoading ? (
                    <Loader2 className={cn("h-4 w-4 animate-spin mr-2")} />
                  ) : (
                    <Sparkles className={cn("h-4 w-4 mr-2")} />
                  )}
                  生成内容
                </button>
              </div>
            )}
          </div>

          {/* AI 建议 */}
          {(suggestion || isLoading) && (
            <div className={cn(
              "space-y-3"
            )}>
              <div className={cn(
                "flex items-center justify-between"
              )}>
                <h4 className={cn(
                  "text-sm font-medium flex items-center"
                )}>
                  <Sparkles className={cn("h-4 w-4 mr-1 text-blue-500")} />
                  AI 建议
                </h4>
                {suggestion && (
                  <button
                    onClick={() => copyToClipboard(suggestion)}
                    className={cn(
                      "p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                    )}
                  >
                    <Copy className={cn("h-3 w-3")} />
                  </button>
                )}
              </div>

              <div className={cn(
                "p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
              )}>
                {isLoading ? (
                  <div className={cn(
                    "space-y-2"
                  )}>
                    <div className={cn(
                      "flex items-center space-x-2 text-blue-600 dark:text-blue-400"
                    )}>
                      <Loader2 className={cn("h-4 w-4 animate-spin")} />
                      <span className={cn("text-sm")}>AI 正在思考中...</span>
                    </div>
                    {suggestion && (
                      <div 
                        ref={suggestionRef}
                        className={cn(
                          "text-sm text-blue-800 dark:text-blue-200 whitespace-pre-wrap max-h-60 overflow-y-auto"
                        )}
                      >
                        {suggestion}
                        <span className={cn(
                          "inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1"
                        )} />
                      </div>
                    )}
                  </div>
                ) : (
                  <div 
                    ref={suggestionRef}
                    className={cn(
                      "text-sm text-blue-800 dark:text-blue-200 whitespace-pre-wrap max-h-60 overflow-y-auto"
                    )}
                  >
                    {suggestion}
                  </div>
                )}
              </div>

              {suggestion && !isLoading && (
                <div className={cn(
                  "flex space-x-2"
                )}>
                  <button
                    onClick={() => selectedText ? onReplace(suggestion) : onInsert(suggestion)}
                    className={cn(
                      "flex-1 flex items-center justify-center p-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    )}
                  >
                    <Check className={cn("h-4 w-4 mr-2")} />
                    {selectedText ? "替换文字" : "插入内容"}
                  </button>
                  
                  {!selectedText && (
                    <button
                      onClick={() => onInsert(suggestion)}
                      className={cn(
                        "flex-1 flex items-center justify-center p-2 border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                      )}
                    >
                      <Plus className={cn("h-4 w-4 mr-2")} />
                      插入内容
                    </button>
                  )}
                  
                  <button
                    onClick={() => handleQuickPrompt("请重新生成一个不同的版本")}
                    className={cn(
                      "p-2 border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                    )}
                  >
                    <RefreshCw className={cn("h-4 w-4")} />
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </motion.div>
    </motion.div>
  )
}
