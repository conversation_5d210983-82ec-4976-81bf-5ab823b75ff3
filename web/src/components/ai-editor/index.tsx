"use client"

import { useState, use<PERSON><PERSON>back, useEffect, forwardRef, useImperative<PERSON>and<PERSON> } from "react"
import { type JSONContent } from "novel"
import { motion, AnimatePresence } from "framer-motion"
import { HeaderBar } from "./ui/HeaderBar"

// 导入我们的扩展和组件
import { EditPane } from "./ui/EditPane"
import type { EditorCommands } from "./model/commands"
import { configureAI } from "@/lib/ai-api"
import { getBackendUrl } from "@/config/app-config"
import "./ai-editor.css"
import { useCompilationService } from "@/hooks/useCompilationService"
import { ReportPane } from "./ui/ReportPane"

interface AIEditorProps {
  initialContent?: JSONContent
  placeholder?: string
  className?: string
  onContentChange?: (content: JSONContent) => void
  onMarkdownChange?: (markdown: string) => void
  onSelectionChange?: (text: string) => void
  showToolbar?: boolean
  defaultMode?: "edit" | "preview" | "report"
  // AI 配置
  aiConfig?: {
    baseUrl?: string
    timeout?: number
  }
  onModeChange?: (mode: "edit" | "preview" | "report") => void
  onTemplatesChange?: (templates: import("@/hooks/useRealtimeCompilation").Template[] | null) => void
}

export interface AIEditorHandle {
  insertText: (text: string) => void;
  replaceText: (text: string) => void;
  navigateTo: (position: number) => void;
  getMarkdown: () => string;
  setMarkdown: (markdown: string) => void;
  focus: () => void;
  compileReport: (force?: boolean, templateId?: string) => Promise<void>;
  setReportTemplate: (templateId: string | null) => void;
  getTemplates: () => import("@/hooks/useRealtimeCompilation").Template[] | null;
  getSelectedTemplate: () => string | null;

}

// 内部编辑器组件，不使用SSR
const AIEditorInternal = forwardRef<AIEditorHandle, AIEditorProps>(({ 
  initialContent,
  placeholder = "开始写作...",
  className = "",
  onContentChange,
  onMarkdownChange,
  onSelectionChange,
  showToolbar = true,
  defaultMode = "edit",
  aiConfig,
  onModeChange,
  onTemplatesChange,
}, ref) => {
  // 配置AI
  useEffect(() => {
    // 使用环境配置作为基础
    const finalConfig = {
      baseUrl: aiConfig?.baseUrl || getBackendUrl(),
      timeout: aiConfig?.timeout || 300000,
    }

    configureAI(finalConfig)
  }, [aiConfig])

  // 编辑器状态
  const [commands, setCommands] = useState<EditorCommands | null>(null)
  const [mode, setMode] = useState<"edit" | "preview" | "report">(defaultMode)
  const [markdown, setMarkdown] = useState("")
  // 移除冗余的 content/vectorBlob/compiledMarkdownRef 状态，简化数据流

  // 编译服务（解耦的高级接口）
  const {
    isCompiling,
    isConnected,
    lastCompiledContent,
    compilationResult,
    progress,
    error: compilationError,
    compile: serviceCompile,
    precompile,
    needsCompilation,
    ensureConnected,
    templates,
    template: selectedReportTemplate,
    setTemplate: setSelectedReportTemplate,
  } = useCompilationService({
    autoPrecompile: true,
    precompileDelay: 1000,
    enableSmartCompilation: true,
  })

  // 报告预览相关逻辑已迁移到 ReportPane

  // AI 交互逻辑已迁移到 EditPane

  // 导出Markdown文件
  const exportMarkdown = useCallback(() => {
    if (!markdown) return

    const blob = new Blob([markdown], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `document-${new Date().toISOString().split('T')[0]}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [markdown])

  // 编译报告（使用编译服务）
  const compileReport = useCallback(async (forceCompile = false, templateId?: string) => {
    if (!markdown) {
      console.warn("没有内容可以编译")
      return
    }

    // 确保连接
    ensureConnected()
    await serviceCompile(markdown, forceCompile, templateId)
  }, [markdown, ensureConnected, serviceCompile])

  // 智能预编译：用户停止编辑后自动预编译
  useEffect(() => {
    if (!markdown || markdown === lastCompiledContent) {
      return
    }
    // 使用编译服务的智能预编译功能
    precompile(markdown)
  }, [markdown, lastCompiledContent, precompile])

  // 报告模式的自动编译触发移动至 ReportPane

  // 快捷键监听已迁移到 EditPane

  // 模式变化时通知外部容器
  useEffect(() => {
    onModeChange?.(mode)
  }, [mode, onModeChange])

  // 模板列表变化时通知外部容器
  useEffect(() => {
    onTemplatesChange?.(templates || null)
  }, [templates, onTemplatesChange])

  useImperativeHandle(ref, () => ({
    insertText: (text) => {
      commands?.insertText(text)
    },
    replaceText: (text) => {
      commands?.replaceText(text)
    },
    navigateTo: (position) => {
      commands?.navigateTo(position)
    },
    getMarkdown: () => {
      return commands?.getMarkdown() || ''
    },
    setMarkdown: (newMarkdown) => {
      commands?.setMarkdown(newMarkdown)
    },
    focus: () => {
      commands?.focus()
    },
    compileReport: async (force, templateId) => {
      await compileReport(force, templateId)
    },
    setReportTemplate: (templateId) => {
      setSelectedReportTemplate(templateId || null)
    },
    getTemplates: () => templates || null,
    getSelectedTemplate: () => selectedReportTemplate || null,

  }));

  return (
    <motion.div
      className={`ai-editor-container h-full flex flex-col ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* 工具栏 */}
      {showToolbar && (
        <HeaderBar
          mode={mode}
          onChangeMode={(m) => setMode(m)}
          onExportMarkdown={exportMarkdown}
          canExport={!!markdown}
          isCompiling={isCompiling}
          progress={progress}
        />
      )}

      {/* 编辑器和预览区域 */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {/* 编辑器区域 */}
          {mode === "edit" && (
            <motion.div
              key="editor"
              className="w-full h-full"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.2 }}
            >
            <EditPane
              initialContent={initialContent}
              placeholder={placeholder}
              onContentChange={onContentChange}
              onMarkdownChange={(md) => {
                setMarkdown(md)
                onMarkdownChange?.(md)
              }}
              onSelectionChange={onSelectionChange}
              onCommandsReady={setCommands}
              contentMarkdown={markdown}
            />
            </motion.div>
          )}

          {/* Markdown输出区域 */}
          {mode === "preview" && (
            <motion.div
              key="preview"
              className="w-full h-full p-4 bg-gray-50 dark:bg-gray-800"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
            <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 h-full flex flex-col">
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Markdown 输出</h3>
              <pre className="text-sm whitespace-pre-wrap font-mono text-gray-800 dark:text-gray-200 flex-1 overflow-auto">
                {markdown || '开始编辑以查看Markdown输出...'}
              </pre>
            </div>
            </motion.div>
          )}

          {/* 报告预览区域 */}
          {mode === "report" && (
            <ReportPane
              markdown={markdown}
              isConnected={isConnected}
              isCompiling={isCompiling}
              progress={progress}
              error={compilationError}
              compilationResult={compilationResult}
              selectedTemplate={selectedReportTemplate || null}
              needsCompilation={needsCompilation}
              compileReport={compileReport}
            />
          )}
        </AnimatePresence>
      </div>

      {/* AI 助手面板已迁移到 EditPane 内部 */}
    </motion.div>
  )
})

AIEditorInternal.displayName = "AIEditorInternal"

// 使用动态导入禁用SSR的主要导出组件
export const AIEditor = forwardRef<AIEditorHandle, AIEditorProps>((props, ref) => {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return (
      <div className={`ai-editor-container ${props.className || ""}`}>
        <div className="w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
          <div className="text-gray-500 dark:text-gray-400">加载编辑器中...</div>
        </div>
      </div>
    )
  }

  return <AIEditorInternal {...props} ref={ref} />
})

AIEditor.displayName = "AIEditor"
