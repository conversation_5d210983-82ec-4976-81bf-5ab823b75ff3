"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Eye, Edit, Download, FileText } from "lucide-react"
import { formatProgress } from "../lib/compile-map"
import type { CompilationProgress } from "@/hooks/useRealtimeCompilation"

type Mode = "edit" | "preview" | "report"

type Props = {
  mode: Mode
  onChangeMode: (m: Mode) => void
  onExportMarkdown: () => void
  canExport: boolean
  isCompiling: boolean
  progress: CompilationProgress | null
}

export function HeaderBar({
  mode,
  onChangeMode,
  onExportMarkdown,
  canExport,
  isCompiling,
  progress,
}: Props) {
  const compilingText = isCompiling ? formatProgress(progress) || "编译中..." : "报告预览"
  return (
    <div className="flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800">
      <div className="flex items-center space-x-2">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">报告编辑器</h2>
      </div>
      <div className="flex items-center space-x-1">
        <Button
          variant={mode === "edit" ? "secondary" : "ghost"}
          size="sm"
          onClick={() => onChangeMode("edit")}
        >
          <Edit className="w-4 h-4 mr-1" /> 编辑
        </Button>
        <Button
          variant={mode === "preview" ? "secondary" : "ghost"}
          size="sm"
          onClick={() => onChangeMode("preview")}
        >
          <Eye className="w-4 h-4 mr-1" /> Markdown输出
        </Button>
        <Button
          variant={mode === "report" ? "secondary" : "ghost"}
          size="sm"
          onClick={() => onChangeMode("report")}
          disabled={isCompiling}
        >
          <FileText className="w-4 h-4 mr-1" />
          {compilingText}
        </Button>
        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />
        <Button variant="ghost" size="sm" onClick={onExportMarkdown} disabled={!canExport}>
          <Download className="w-4 h-4 mr-1" /> 导出
        </Button>
      </div>
    </div>
  )
}

