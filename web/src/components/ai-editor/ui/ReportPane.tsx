"use client"

import { useEffect, useMemo } from "react"
import dynamic from "next/dynamic"
import { Button } from "@/components/ui/button"
import type { CompilationResult, CompilationProgress } from "@/hooks/useRealtimeCompilation"
import { formatProgress } from "../lib/compile-map"
import { base64ToBlob } from "../lib/vector"

const TypstCoreViewer = dynamic(() => import("@/app/components/TypstCoreViewer"), { ssr: false })

type Props = {
  markdown: string
  isConnected: boolean
  isCompiling: boolean
  progress: CompilationProgress | null
  error: string | null
  compilationResult: CompilationResult | null
  selectedTemplate: string | null
  needsCompilation: (content: string) => boolean
  compileReport: (force?: boolean, templateId?: string) => void | Promise<void>
}

export function ReportPane({
  markdown,
  isConnected,
  isCompiling,
  progress,
  error,
  compilationResult,
  selectedTemplate,
  needsCompilation,
  compileReport,
}: Props) {
  // Compute vector Blob lazily when result changes
  const vectorBlob = useMemo(() => {
    if (!compilationResult?.vectorData) return null
    try {
      return base64ToBlob(compilationResult.vectorData)
    } catch (e) {
      console.error("向量数据解析失败:", e)
      return null
    }
  }, [compilationResult])

  // Trigger compile when entering report mode and needed
  useEffect(() => {
    if (!markdown) return
    if (isConnected && needsCompilation(markdown)) {
      compileReport(false, selectedTemplate || "")
    }
  }, [markdown, isConnected, selectedTemplate, needsCompilation, compileReport])

  return (
    <div className="w-full h-full p-4 bg-gray-50 dark:bg-gray-800">
      <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 h-full flex flex-col">
        <div className="flex-1 overflow-auto">
          {isCompiling && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">编译中...</p>
                <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">Markdown → Typst → Vector</p>
                {progress && (
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    {formatProgress(progress)}
                  </p>
                )}
              </div>
            </div>
          )}

          {!isCompiling && error && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-red-600 dark:text-red-400 mb-4">编译失败</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => compileReport(true, selectedTemplate || "")}
                  className="mt-4"
                >
                  重试
                </Button>
              </div>
            </div>
          )}

          {!isCompiling && !error && vectorBlob && (
            <TypstCoreViewer
              artifactBlob={vectorBlob}
              className="w-full h-full"
              fill="#ffffff"
            />
          )}

          {!isCompiling && !error && !vectorBlob && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                {!markdown ? (
                  <>
                    <p className="text-gray-600 dark:text-gray-400 mb-2">请先在编辑器中输入内容</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">切换到编辑模式开始写作</p>
                  </>
                ) : (
                  <>
                    <p className="text-gray-600 dark:text-gray-400 mb-2">等待首次编译结果或开始编辑以触发</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">实时模式：Markdown → Typst → Vector</p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mb-4">
                      {isConnected ? 'WebSocket 已连接' : '正在连接 WebSocket...'}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => compileReport(false, selectedTemplate || '')}
                      disabled={!isConnected}
                    >
                      {isConnected ? '开始编译' : '连接中...'}
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
