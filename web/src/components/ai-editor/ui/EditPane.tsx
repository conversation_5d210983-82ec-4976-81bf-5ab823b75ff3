"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  Editor<PERSON><PERSON>nt,
  EditorCommand,
  EditorCommandEmpty,
  EditorCommandItem,
  EditorCommandList,
  ImageResizer,
  type JSONContent,
  type EditorInstance,
  handleCommandNavigation,
} from 'novel'
import { useDebouncedCallback } from 'use-debounce'
import { AIToolbar } from '../ai-toolbar'
import dynamic from 'next/dynamic'
import { suggestionItems } from '../slash-command'
import { useAI } from '@/hooks/use-ai'
import { Sparkles } from 'lucide-react'
import { aiEditorExtensions } from '../extensions'
import type { EditorCommands } from '../model/commands'
import { createEditorCommands } from '../model/commands'
import type { EditorLike } from '../model/commands'
import { useAIShortcuts } from '../model/useAIShortcuts'

type Props = {
  initialContent?: JSONContent
  placeholder?: string
  onContentChange?: (content: JSONContent) => void
  onMarkdownChange?: (markdown: string) => void
  onSelectionChange?: (text: string) => void
  onCommandsReady?: (commands: EditorCommands) => void
  // 用于在组件重新挂载时恢复内容（从父级传入最新的 markdown）
  contentMarkdown?: string
}

export function EditPane({
  initialContent,
  placeholder = '开始写作...',
  onMarkdownChange,
  onContentChange,
  onSelectionChange,
  onCommandsReady,
  contentMarkdown,
}: Props) {
  const [editor, setEditor] = useState<EditorLike | null>(null)
  const [isAIOpen, setIsAIOpen] = useState(false)
  const [selectedText, setSelectedText] = useState('')

  // AI Hook
  const { isLoading, completion: aiSuggestion, executeCommand, reset: resetAI } = useAI({
    onError: (error) => {
      console.error('AI 生成失败:', error)
    },
    onComplete: (result) => {
      console.log('AI 生成完成:', result)
    },
  })

  // 生成命令对象，暴露给父组件
  useEffect(() => {
    if (editor && onCommandsReady) {
      onCommandsReady(createEditorCommands(editor))
    }
  }, [editor, onCommandsReady])

  const debouncedUpdate = useDebouncedCallback((ed: EditorLike) => {
    const json = ed.getJSON()
    onContentChange?.(json)
    const markdownContent = ed.storage.markdown.getMarkdown()
    onMarkdownChange?.(markdownContent)
  }, 300)

  const handleAIClick = useCallback(() => {
    if (!editor) return
    const { selection } = editor.state
    const text = editor.state.doc.textBetween(selection.from, selection.to)
    setSelectedText(text)
    setIsAIOpen(true)
  }, [editor])

  const generateAIText = useCallback(
    async (prompt: string, context?: string) => {
      try {
        resetAI()
        await executeCommand(
          {
            id: 'custom',
            type: 'custom',
            label: '自定义生成',
            description: '自定义AI生成',
            icon: Sparkles,
            prompt,
            requiresSelection: false,
            category: 'generate',
          },
          context,
          prompt,
        )
      } catch (error) {
        console.error('AI 生成失败:', error)
      }
    },
    [executeCommand, resetAI],
  )

  const insertAIText = useCallback(
    (text: string) => {
      if (!editor) return
      editor.chain().focus().insertContent(text).run()
      setIsAIOpen(false)
      resetAI()
    },
    [editor, resetAI],
  )

  const replaceSelectedText = useCallback(
    (text: string) => {
      if (!editor) return
      const { selection } = editor.state
      editor.chain().focus().deleteRange({ from: selection.from, to: selection.to }).insertContent(text).run()
      setIsAIOpen(false)
      resetAI()
    },
    [editor, resetAI],
  )

  // 快捷键：打开 AI 助手
  useAIShortcuts(handleAIClick, [handleAIClick])

  return (
    <EditorRoot>
      <EditorContent
        immediatelyRender={false}
        initialContent={initialContent}
        extensions={aiEditorExtensions}
        className="h-full w-full overflow-auto"
        editorProps={{
          handleDOMEvents: {
            keydown: (_view, event) => handleCommandNavigation(event),
          },
          attributes: {
            class: 'prose prose-base dark:prose-invert max-w-none p-4 focus:outline-hidden min-h-full',
            'data-placeholder': placeholder,
          },
        }}
        onCreate={({ editor }) => {
          const ed = editor as unknown as EditorLike
          setEditor(ed)
          // 若父级提供了最新的 markdown（例如切换模式后重新挂载），则以其为准恢复内容
          if (contentMarkdown && contentMarkdown.trim().length > 0) {
            ed.commands.setContent(contentMarkdown)
          }
        }}
        onUpdate={({ editor }) => {
          debouncedUpdate(editor as unknown as EditorLike)
        }}
        onSelectionUpdate={({ editor }) => {
          const { from, to } = editor.state.selection
          const text = editor.state.doc.textBetween(from, to, ' ')
          onSelectionChange?.(text)
        }}
      >
        {/* Slash 命令菜单 */}
        <EditorCommand className="z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all">
          <EditorCommandEmpty className="px-2 text-gray-500 dark:text-gray-400">没有找到结果</EditorCommandEmpty>
          <EditorCommandList>
            {suggestionItems.map((item) => (
              <EditorCommandItem
                value={item.title}
                onCommand={(val) => item.command?.(val)}
                className="flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700"
                key={item.title}
              >
                <div className="flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                  {item.icon}
                </div>
                <div>
                  <p className="font-medium">{item.title}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{item.description}</p>
                </div>
              </EditorCommandItem>
            ))}
          </EditorCommandList>
        </EditorCommand>

        {/* AI 工具栏 */}
        <AIToolbar editor={editor as unknown as EditorInstance | null} onAIClick={handleAIClick} isLoading={isLoading} />

        {/* 图片调整器 */}
        <ImageResizer />
      </EditorContent>

      {/* AI 助手面板 */}
      {isAIOpen && (
        <DynamicAIAssistant
          selectedText={selectedText}
          suggestion={aiSuggestion}
          isLoading={isLoading}
          onGenerate={generateAIText}
          onInsert={insertAIText}
          onReplace={replaceSelectedText}
          onClose={() => setIsAIOpen(false)}
        />
      )}
    </EditorRoot>
  )
}

const DynamicAIAssistant = dynamic(() => import('../ai-assistant').then(m => m.AIAssistant), {
  ssr: false,
  loading: () => null,
})
