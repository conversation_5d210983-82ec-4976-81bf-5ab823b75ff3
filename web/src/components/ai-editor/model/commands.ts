/* eslint-disable @typescript-eslint/no-explicit-any */
import type { JSONContent } from 'novel'

export interface EditorCommands {
  insertText: (text: string) => void
  replaceText: (text: string) => void
  navigateTo: (position: number) => void
  getMarkdown: () => string
  setMarkdown: (markdown: string) => void
  focus: () => void
}
// Define a minimal structural type of the editor we need
type PMSelection = { from: number; to: number }
type PMDoc = { textBetween: (from: number, to: number, sep?: string) => string }
type PMState = { selection: PMSelection; doc: PMDoc }
type Chainable = {
  focus: () => Chainable
  insertContent: (text: string) => Chainable
  deleteRange: (range: { from: number; to: number }) => Chainable
  setTextSelection: (pos: number) => Chainable
  run: () => void
}
type Commands = { setContent: (md: string) => void; focus: () => void }
type MarkdownStore = { getMarkdown: () => string }
export type EditorLike = {
  chain: () => Chainable
  commands: Commands
  state: PMState
  storage: Record<string, unknown> & { markdown: MarkdownStore }
  getJSON: () => JSONContent
}

export function createEditorCommands(editor: EditorLike): EditorCommands {
  return {
    insertText: (text: string) => {
      editor.chain().focus().insertContent(text).run()
    },
    replaceText: (text: string) => {
      const { from, to } = editor.state.selection
      editor.chain().focus().deleteRange({ from, to }).insertContent(text).run()
    },
    navigateTo: (position: number) => {
      editor.chain().focus().setTextSelection(position).run()
    },
    getMarkdown: () => {
      return editor.storage.markdown.getMarkdown() || ''
    },
    setMarkdown: (markdown: string) => {
      editor.commands.setContent(markdown)
    },
    focus: () => {
      editor.commands.focus()
    },
  }
}
