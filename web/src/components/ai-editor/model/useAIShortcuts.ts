import { useEffect } from 'react'

export function useAIShortcuts(onAssistantTrigger: () => void, deps: unknown[] = []) {
  useEffect(() => {
    const handler = (event: Event) => {
      const type = (event as CustomEvent).type
      if (type === 'ai-assistant-trigger') onAssistantTrigger()
      if (type === 'ai-quick-generate') onAssistantTrigger()
    }
    document.addEventListener('ai-assistant-trigger', handler)
    document.addEventListener('ai-quick-generate', handler)
    return () => {
      document.removeEventListener('ai-assistant-trigger', handler)
      document.removeEventListener('ai-quick-generate', handler)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps)
}

