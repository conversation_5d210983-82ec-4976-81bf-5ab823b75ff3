"use client"

import { cn } from "@/lib/utils"
import Image from "next/image"
import React from "react"

export interface TemplateInfo {
  id: string
  name: string
  description: string
  previewImage?: string
  preview?: React.ReactNode
}

interface TemplateBarProps {
  templates: TemplateInfo[]
  selectedTemplate: string | null
  setTemplate: (templateId: string | null) => void
}

function Placeholder({ name }: { name: string }) {
  return (
    <div className="flex h-24 w-full items-center justify-center rounded bg-gray-100 dark:bg-gray-700">
      <div className="text-center">
        <div className="mb-1 text-xs text-gray-500 dark:text-gray-400">Preview</div>
        <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">{name}</div>
      </div>
    </div>
  )
}

function TemplateCard({ template }: { template: TemplateInfo }) {
  const [imageFailed, setImageFailed] = React.useState(false)
  const shouldShowImage = !!template.previewImage && !imageFailed

  return (
    <>
      <div className="relative mb-2 h-24 w-full overflow-hidden rounded">
        {shouldShowImage ? (
          <Image
            src={template.previewImage!}
            alt={`${template.name} preview`}
            fill
            sizes="(max-width: 768px) 100vw, 400px"
            className="object-cover"
            onError={() => setImageFailed(true)}
            unoptimized={process.env.NODE_ENV === "development"}
            priority={false}
          />
        ) : (
          <Placeholder name={template.name} />
        )}
      </div>
      <div className="font-semibold">{template.name}</div>
      <div className="text-sm text-gray-500 dark:text-gray-400">
        {template.description}
      </div>
    </>
  )
}

export function TemplateBar({ templates, selectedTemplate, setTemplate }: TemplateBarProps) {
  const handleSelect = (id: string) => {
    if (selectedTemplate === id) {
      setTemplate(null)
    } else {
      setTemplate(id)
    }
  }

  return (
    <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
      {templates.map((template) => (
        <button
          key={template.id}
          onClick={() => handleSelect(template.id)}
          className={cn(
            "flex flex-col items-start rounded-lg border p-4 text-left transition-colors",
            selectedTemplate === template.id
              ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
              : "border-gray-200 hover:border-blue-400 dark:border-gray-700 dark:hover:border-blue-500"
          )}
        >
          {template.preview ? (
            <div className="mb-2 w-full">{template.preview}</div>
          ) : (
            <TemplateCard template={template} />
          )}
        </button>
      ))}
    </div>
  )
}