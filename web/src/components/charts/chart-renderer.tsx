"use client"

import { useState, useEffect } from 'react'
import DOMPurify from 'dompurify'
import { apiClient } from '@/lib/api-client'
import { cn } from '@/lib/utils'

interface ChartConfig {
  type: string
  title?: string
  data?: Record<string, unknown>
  datasets?: ChartDataset[]
  categories?: string[]
  error?: string
}

interface ChartDataset {
  label?: string
  data: number[]
  value?: number
  name?: string
  type?: string
}

interface ChartRendererProps {
  chartConfig: ChartConfig
  className?: string
}

export function ChartRenderer({ chartConfig, className = '' }: ChartRendererProps) {
  const [svgContent, setSvgContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const generateSVG = async () => {
      try {
        setIsLoading(true)
        setError(null)
        const svg = await apiClient.generateChartSVG(chartConfig)
        setSvgContent(svg)
      } catch (err) {
        setError(err instanceof Error ? err.message : '生成图表失败')
        // 生成错误SVG作为fallback
        const errorSvg = await apiClient.generateChartSVG({
          type: 'error',
          title: chartConfig.title || '图表',
          error: '生成失败'
        })
        setSvgContent(errorSvg)
      } finally {
        setIsLoading(false)
      }
    }

    if (chartConfig) {
      generateSVG()
    }
  }, [chartConfig])

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center p-4 border rounded-lg bg-gray-50", className)}>
        <div className="text-gray-500">加载图表中...</div>
      </div>
    )
  }

  if (error && !svgContent) {
    return (
      <div className={cn("flex items-center justify-center p-4 border rounded-lg bg-red-50", className)}>
        <div className="text-red-500">图表加载失败: {error}</div>
      </div>
    )
  }

  // 安全过滤SVG内容
  const sanitizedSVG = svgContent ? DOMPurify.sanitize(svgContent, {
    ALLOWED_TAGS: ['svg', 'path', 'circle', 'rect', 'line', 'text', 'g', 'defs', 'pattern'],
    ALLOWED_ATTR: ['width', 'height', 'viewBox', 'd', 'fill', 'stroke', 'stroke-width', 'cx', 'cy', 'r', 'x', 'y', 'font-size', 'font-weight', 'text-anchor', 'class', 'id']
  }) : ''

  return (
    <div className={cn("inline-block", className)}>
      <div 
        dangerouslySetInnerHTML={{ __html: sanitizedSVG }}
        className="max-w-full"
      />
    </div>
  )
}

interface TableRendererProps {
  tableData: {
    headers: string[]
    rows: string[][]
    title?: string
  }
  className?: string
}

export function TableRenderer({ tableData, className = '' }: TableRendererProps) {
  const { headers, rows, title } = tableData

  return (
    <div className={cn("my-4", className)}>
      {title && (
        <h3 className="text-lg font-semibold mb-2 text-gray-800">{title}</h3>
      )}
      <div className="overflow-x-auto border rounded-lg">
        <table className="min-w-full bg-white">
          {headers && headers.length > 0 && (
            <thead className="bg-gray-50">
              <tr>
                {headers.map((header, index) => (
                  <th
                    key={index}
                    className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
          )}
          <tbody>
            {rows.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
              >
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-4 py-2 text-sm text-gray-900 border-b"
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}