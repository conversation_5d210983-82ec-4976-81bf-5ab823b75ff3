/* Report Editor 样式增强 */

/* 拖拽手柄样式优化 */
.resizable-handle {
  position: relative;
  transition: all 0.2s ease;
}

.resizable-handle:hover {
  background: linear-gradient(to bottom, #60a5fa, #3b82f6);
}

.resizable-handle:active {
  background: linear-gradient(to bottom, #3b82f6, #2563eb);
}

/* 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 大纲项目悬停效果 */
.outline-item {
  transition: all 0.15s ease;
  position: relative;
}

.outline-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: transparent;
  transition: background 0.15s ease;
}

.outline-item:hover::before {
  background: #3b82f6;
}

.outline-item.active::before {
  background: #2563eb;
}

/* 模板选择器下拉动画 */
.template-dropdown {
  animation: slideDown 0.2s ease;
  transform-origin: top;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: scaleY(0.95) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: scaleY(1) translateY(0);
  }
}

/* AI面板消息动画 */
.message-enter {
  animation: messageSlideIn 0.3s ease;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态动画 */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 状态指示器 */
.status-indicator {
  position: relative;
  overflow: hidden;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 工具栏按钮状态 */
.toolbar-button {
  transition: all 0.15s ease;
  position: relative;
}

.toolbar-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.toolbar-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

/* 响应式断点 */
@media (max-width: 1024px) {
  .resizable-panel-group {
    flex-direction: column;
  }
  
  .resizable-handle {
    height: 4px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .toolbar-button {
    padding: 0.5rem;
  }
  
  .toolbar-button span {
    display: none;
  }
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
  .custom-scrollbar {
    scrollbar-color: rgba(107, 114, 128, 0.5) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(107, 114, 128, 0.5);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 0.7);
  }
}

/* 聚焦状态优化 */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 文本选择样式 */
::selection {
  background: rgba(59, 130, 246, 0.3);
}

::-moz-selection {
  background: rgba(59, 130, 246, 0.3);
}

/* Line clamp 工具类 */
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}