# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
Always respond in English.

## 1) Purpose & Role

This document instructs Claude <PERSON> how to work in this repository: what the system does, where key code lives, how to run/test it, security expectations, and the reply format for proposed changes.

## 2) Project Snapshot

AI Report Generator — converts Word/PDF into structured Markdown + charts, provides SSE-based AI writing assistance, and offers Typst real-time vector compilation with a web preview.

- Backend: Python 3.13, FastAPI, SQLAlchemy, Alembic, MySQL (PyMySQL)
- Frontend: Next.js 15, React 18, TypeScript, TipTap/Novel, Typst.ts ,d3.js
- Streaming: SSE (AI writing), WebSocket (Typst real-time compile)
- AI/LLM: Volcano Engine API, openai, streaming responses
- Typst: typst-ts-cli (vector artifacts .artifact.sir.in), fonts via TYPST_FONT_PATHS,rust-tools/extract-tool (Python bindings)
- word format  mammoth, python-docx
- pdf format coze workflow
- Charts: D3.js config generation
- Tooling: uv (preferred), pnpm, structlog + Rich logging, optional Redis client


## 3) Runbook (dev)
```bash
# Backend
uv install
alembic upgrade head
uv run python main.py          # http://localhost:8000/docs

# Typst prerequisites
uv run python setup_fonts.py   # optional but recommended for CJK
# Ensure the CLI is available in PATH
typst-ts-cli --version

# Frontend
cd web
pnpm install
pnpm dev        # http://localhost:3000
pnpm run type-check       
pnpm build 
pnpm lint
```

## 4) Repository Map (essentials)

- main.py — FastAPI app, CORS/middleware/errors
**api/**

- auth_routes.py — JWT login/refresh, RBAC guards
- report_routes.py — charts config
- word_format_routes.py — Word upload + processing
- pdf_routes.py — PDF upload & Coze workflow processing
- markdown_to_typst_routes.py — Markdown to Typst conversion endpoints
- ai_stream_routes.py — SSE streaming endpoints
- typst_compile_routes.py — HTTP: `/api/typst-compile/{health,compile-full,test-conversion}`
- websocket_routes.py — WS: `/ws/compile/{session_id}` (connection/progress/result)

**services/**

- word_parser.py — Word → Markdown (tables → placeholders)
- pdf_parser.py — PDF → Markdown via Coze workflow API
- chart_xml_parser.py — Chart XML metadata extraction
- word_image_extractor.py — Images & chart data from Word
- d3js_generator.py — XML → D3.js configs
- chart_service.py — Orchestrates doc → chart pipeline
- markdown_to_typst_service.py — Markdown to Typst conversion service
- ai_chat_service.py — LLM calls, optimization & text ops
- auth_service.py — JWT/session mgmt

**core/**

- security.py — JWT token validation and RBAC decorators / OAuth2 bearer helpers
- rate_limit.py — Memory sliding window rate limiting (used by compile-full)
- security_headers.py — OWASP security headers middleware
- request_context.py — Request context and disconnect detection
- logging.py — structlog + Rich, SQLAlchemy noise suppression

**database/**

- connections & in-memory store
- connection.py — SQLAlchemy session
- redis_client.py — optional Redis client with PING self-check

**models/**

- SQLAlchemy models & Pydantic schemas

**alembic/**

- DB migrations

**web/src/**

- components/ai-editor/ — TipTap/Novel editor & AI UI
- components/chart-renderer.tsx — Renders D3 charts from backend configs
- components/markdown-renderer.tsx — Markdown with `[CHART:chart_id]`
- components/TypstCoreViewer.tsx — Typst document preview component / vector preview
- components/templates/... — Template selectors (Typst/Markdown)
- hooks/useRealtimeCompilation.ts — WS + health orchestration
- hooks/useCompilationService.ts — frontend service wrapper
- lib/ai-api.ts
- hooks/use-ai.ts — SSE client & state mgmt
- lib/api-client.ts — Injects Bearer tokens
- lib/server-auth.ts — central cookie handling
- app/api/ — Next.js API routes (unified service layer)
- app/api/prose/generate/route.ts — Dev proxy (CORS-free)
- app/api/auth/* — HttpOnly cookie auth (Next.js server routes)
- app/api/typst-compile/* — proxy to backend compile endpoints
- app/components/TypstCoreViewer.tsx — vector preview
- lib/server-auth.ts — central cookie handling
## 5) Architecture (at a glance)

- Word (.doc/.docx) / PDF
   → mammoth/python-docx extract text/tables/images
   → Coze workflow API for PDF → Markdown conversion
   → chart_xml_parser + word_image_extractor detect chart metadata
   → d3js_generator builds D3-compatible JSON
   → FastAPI serves Markdown + chart configs
   → Next.js API routes (unified service layer) proxy requests
   → Frontend renders Markdown + D3
   → AI streaming (SSE) provides real-time writing assistance
   → Rust-based Typst engine converts Markdown to professional PDF
- **Extended pipeline**:
   Word/PDF → parse/extract → AI optimize → Markdown + chart configs → SSE-assisted editing → WS-triggered compile → Typst vector artifact → web preview/export
- **Non-blocking policy**:
   Typst compilation runs in a small thread pool; WS loop must not block. Health endpoint reflects executor readiness. Fonts resolved via `fonts/` + `/tmp/typst-fonts` (`TYPST_FONT_PATHS`).

- Templates: Use Typst native import and `#show: template`; see `docs/SIMPLE_TEMPLATE_SYSTEM.md`.

------

## 6) Security Expectations (enforced)

- **Auth**:
  - JWT Bearer + refresh; sessions rotate; bcrypt passwords; RBAC roles.
  - Frontend stores tokens in HttpOnly cookies via Next.js server routes.
- **RBAC**: Must use `core/security.py` helpers; do not bypass.
- **Rate limits**:
  - login ~10/60s
  - refresh ~30/60s
  - AI gen ~60/60s
  - compile-full guarded by sliding-window limiter
- **Headers**: OWASP-aligned (X-Content-Type-Options, Referrer-Policy, X-Request-ID).
- **Uploads**: Path traversal blocked; only `.doc/.docx`; magic-number validation.
- **Streaming**:
  - SSE requires valid Bearer token; WS & SSE must handle disconnects cleanly.
  - Event loop must not block.
- **Secrets**: Never hardcode; use `.env` and `config.py` (pydantic settings).

## 7) Response Contract (how to reply)
- Plan: 3–7 concise steps (what/why).
- Diffs: unified diffs per file; small, focused hunks.
- Commands: exact shell lines to run (backend/frontend/migrations).
- Verification: steps + expected results (incl. WS/SSE and auth flows).
- Rollback: e.g., `alembic downgrade -1` or route toggle.
Keep replies succinct; prefer diffs over prose.


## 8) Coding Conventions & Guardrails

- Python 3.13 compatibility; prefer `uv run`.
- FastAPI: DI-first; typed Pydantic models; explicit error handling.
- SQLAlchemy: explicit models; avoid N+1; migrations only via Alembic.
- Streaming: yield promptly; never block event loop; keep timeouts; retries when valuable.
- Frontend: functional React with TypeScript; small, testable components.
- TipTap extensions isolated; no client-side secret storage.
- Component best practices:
  - Use semantic HTML elements (button, div with appropriate roles)
  - Implement proper accessibility (ARIA labels, keyboard navigation)
  - Use CSS Grid/Flexbox for responsive layouts instead of fixed dimensions
  - Prefer toggle interactions (click to select/deselect)
  - Support multiple content types (images, React nodes) via props
  - Use utility functions like `cn()` for conditional class names
  - Implement proper dark mode support with dark: variants
- Charts: D3 configs must be pure JSON; rendering responsive.
- Typst: assume `typst-ts-cli` available; set `TYPST_FONT_PATHS`.
- Do not widen CORS, weaken rate limits, or bypass RBAC.

## 9) Test Guidance (what “done” looks like)
- Backend smoke: server runs; `/api/typst-compile/health` returns healthy/initializing.
- WS path: connect → `connection_established`; send ping → `pong`; no blocking in handlers.
- Compile path: with auth + rate-limit respected, returns vector bytes; errors are structured.
- Frontend: editor receives progress → complete; viewer renders without layout shifts.
