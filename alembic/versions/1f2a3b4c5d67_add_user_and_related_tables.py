"""Add users, user_sessions, query_history, user_stats tables

Revision ID: 1f2a3b4c5d67
Revises: d80b4764e1cd
Create Date: 2025-08-14 10:00:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1f2a3b4c5d67'
down_revision: Union[str, Sequence[str], None] = 'd80b4764e1cd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema: create auth and audit tables."""
    # users
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('email', sa.String(length=100), nullable=False),
        sa.Column('full_name', sa.String(length=100), nullable=True),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('role', sa.String(length=20), nullable=False, server_default='user'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default=sa.text('1')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username', name='uq_users_username'),
        sa.UniqueConstraint('email', name='uq_users_email'),
    )
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)

    # user_sessions
    op.create_table(
        'user_sessions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('session_token', sa.String(length=255), nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default=sa.text('1')),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('last_accessed', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('session_token', name='uq_user_sessions_token'),
    )
    op.create_index(op.f('ix_user_sessions_user_id'), 'user_sessions', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_sessions_session_token'), 'user_sessions', ['session_token'], unique=True)

    # query_history
    op.create_table(
        'query_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('conversation_id', sa.String(length=100), nullable=True),
        sa.Column('query_text', sa.Text(), nullable=False),
        sa.Column('intent_type', sa.String(length=20), nullable=False),
        sa.Column('generated_sql', sa.Text(), nullable=True),
        sa.Column('response_text', sa.Text(), nullable=False),
        sa.Column('is_successful', sa.Boolean(), nullable=False, server_default=sa.text('0')),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('execution_time', sa.Float(), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('quality_score', sa.Float(), nullable=True),
        sa.Column('is_auto_recorded', sa.Boolean(), nullable=False, server_default=sa.text('0')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(op.f('ix_query_history_user_id'), 'query_history', ['user_id'], unique=False)
    op.create_index(op.f('ix_query_history_conversation_id'), 'query_history', ['conversation_id'], unique=False)

    # user_stats
    op.create_table(
        'user_stats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('total_queries', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('successful_queries', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('failed_queries', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('total_execution_time', sa.Float(), nullable=False, server_default=sa.text('0')),
        sa.Column('avg_response_time', sa.Float(), nullable=False, server_default=sa.text('0')),
        sa.Column('last_query_time', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', name='uq_user_stats_user_id'),
    )
    op.create_index(op.f('ix_user_stats_user_id'), 'user_stats', ['user_id'], unique=True)


def downgrade() -> None:
    """Downgrade schema: drop created tables."""
    op.drop_index(op.f('ix_user_stats_user_id'), table_name='user_stats')
    op.drop_table('user_stats')

    op.drop_index(op.f('ix_query_history_conversation_id'), table_name='query_history')
    op.drop_index(op.f('ix_query_history_user_id'), table_name='query_history')
    op.drop_table('query_history')

    op.drop_index(op.f('ix_user_sessions_session_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_user_id'), table_name='user_sessions')
    op.drop_table('user_sessions')

    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')


