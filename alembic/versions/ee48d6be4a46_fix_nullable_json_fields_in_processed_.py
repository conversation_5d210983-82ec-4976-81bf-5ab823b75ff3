"""fix nullable JSON fields in processed_documents

Revision ID: ee48d6be4a46
Revises: 1f2a3b4c5d67
Create Date: 2025-08-26 10:30:58.778610

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ee48d6be4a46'
down_revision: Union[str, Sequence[str], None] = '1f2a3b4c5d67'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('processed_documents', 'tables',
               existing_type=mysql.JSON(),
               nullable=True)
    op.alter_column('processed_documents', 'charts',
               existing_type=mysql.JSON(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('processed_documents', 'charts',
               existing_type=mysql.JSON(),
               nullable=False)
    op.alter_column('processed_documents', 'tables',
               existing_type=mysql.JSON(),
               nullable=False)
    # ### end Alembic commands ###
