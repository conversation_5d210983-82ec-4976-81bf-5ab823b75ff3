"""Fix nullable JSON fields in processed_documents

Revision ID: 59922e8ebfa7
Revises: 1f2a3b4c5d67
Create Date: 2025-08-15 15:47:20.432592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '59922e8ebfa7'
down_revision: Union[str, Sequence[str], None] = '1f2a3b4c5d67'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('processed_documents', 'tables',
               existing_type=mysql.JSON(),
               nullable=False)
    op.alter_column('processed_documents', 'charts',
               existing_type=mysql.JSON(),
               nullable=False)
    op.drop_constraint(op.f('fk_query_history_user_id'), 'query_history', type_='foreignkey')
    op.drop_constraint(op.f('fk_user_sessions_user_id'), 'user_sessions', type_='foreignkey')
    op.drop_constraint(op.f('fk_user_stats_user_id'), 'user_stats', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(op.f('fk_user_stats_user_id'), 'user_stats', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('fk_user_sessions_user_id'), 'user_sessions', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('fk_query_history_user_id'), 'query_history', 'users', ['user_id'], ['id'], ondelete='SET NULL')
    op.alter_column('processed_documents', 'charts',
               existing_type=mysql.JSON(),
               nullable=True)
    op.alter_column('processed_documents', 'tables',
               existing_type=mysql.JSON(),
               nullable=True)
    # ### end Alembic commands ###
