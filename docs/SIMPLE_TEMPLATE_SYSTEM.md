# 简化的 Typst 模板系统

## 设计理念

基于你的实际需求，我重新设计了一个**极简而实用**的模板系统，完全遵循 Typst 官方的模板模式，避免了复杂的动态转换逻辑。

## 核心原理

### 1. Typst 官方模板模式
```typst
// 模板定义
#let template(doc) = [
  #set text(font: "SimSun")
  #set page(margin: 2cm)
  #doc
]

// 模板应用
#import "template.typ": template
#show: template

// 用户内容
= 标题
内容...
```

### 2. 我们的实现方式
```python
# 生成最终内容
final_content = f'''#import "{relative_path}": template

#show: template

{typst_content}'''
```

## 文件结构

```
assets/templates/
├── templates.json          # 模板配置文件
├── travel/
│   └── travel.typ         # 旅行报告模板
└── simple/
    └── simple.typ         # 简洁模板
```

## 模板配置

### templates.json
```json
[
  {
    "id": "travel",
    "name": "旅行报告模板",
    "description": "适用于旅行游记、行程报告的简洁模板",
    "preview": "preview/travel.svg",
    "typst_file": "travel/travel.typ"
  }
]
```

**关键改进**：
- 删除了复杂的 `capabilities` 配置
- `typst_file` 字段真正有效，支持任意路径
- 配置简洁明了，易于维护

## 模板文件示例

### travel.typ（旅行模板）
```typst
#let template(doc) = [
  // 页面设置
  #set page(
    paper: "a4",
    margin: (x: 2.5cm, y: 2cm),
    numbering: "1",
    number-align: center,
  )
  
  // 字体设置
  #set text(
    font: ("Times New Roman", "SimSun"),
    size: 12pt,
    lang: "zh"
  )
  
  // 标题样式
  #show heading.where(level: 1): it => [
    #set align(center)
    #set text(size: 24pt, font: "SimSun", weight: "bold")
    #block(above: 24pt, below: 16pt, it.body)
  ]

  // 段落设置
  #set par(
    justify: true,
    first-line-indent: 2em,
    leading: 0.65em
  )
  
  // 插入文档内容
  #doc
]
```

**特点**：
- 纯 Typst 语法，无需学习额外 API
- 遵循官方模板模式，兼容性好
- 样式设置清晰，易于定制

## 应用流程

### 1. cmarker 转换
```
Markdown → cmarker_python → Typst 内容
```

### 2. 模板应用
```python
def apply_template_to_content(self, template_id: str, typst_content: str) -> str:
    # 1. 读取模板配置
    # 2. 构建导入路径
    # 3. 生成最终内容
    final_content = f'''#import "{relative_path}": template

#show: template

{typst_content}'''
```

### 3. 最终编译
```
带模板的 Typst 内容 → typst-ts-cli → PDF/SVG
```

## 优势对比

### ❌ 之前的复杂方案
- 需要复杂的能力分析
- 动态内容转换（需要 AI 或复杂正则）
- 自定义的模板管理器
- 过度设计，维护困难

### ✅ 现在的简化方案
- **零学习成本**：完全遵循 Typst 官方模式
- **零动态转换**：直接使用 cmarker 输出
- **零复杂逻辑**：简单的字符串拼接
- **完全兼容**：可以直接用 typst-ts-cli 编译

## 扩展方式

### 1. 添加新模板
```bash
# 1. 创建模板目录
mkdir assets/templates/academic

# 2. 创建模板文件
cat > assets/templates/academic/academic.typ << 'EOF'
#let template(doc) = [
  // 学术论文样式
  #set text(font: "Times New Roman")
  #doc
]
EOF

# 3. 更新配置
# 在 templates.json 中添加新条目
```

### 2. 模板继承
```typst
// base.typ
#let base_template(doc) = [
  #set text(font: "SimSun")
  #doc
]

// academic.typ
#import "base.typ": base_template

#let template(doc) = [
  #show: base_template
  // 添加学术特定样式
  #set page(numbering: "1")
  #doc
]
```

### 3. 参数化模板
```typst
#let template(
  title: none,
  author: none,
  doc
) = [
  #if title != none [
    #align(center, text(18pt, weight: "bold", title))
  ]
  #if author != none [
    #align(center, text(12pt, author))
  ]
  #doc
]
```

## 使用示例

### 1. 基本使用
```python
from services.markdown_to_typst_service import MarkdownToTypstService

service = MarkdownToTypstService()

# Markdown → Typst
typst_content = service.convert_markdown_to_typst(markdown_text)

# 应用模板
final_content = service.apply_template_to_content("travel", typst_content)

# 保存并编译
with open("output.typ", "w") as f:
    f.write(final_content)
```

### 2. API 调用
```bash
# 编译时指定模板
curl -X POST /api/typst-compile/compile \
  -H "Content-Type: application/json" \
  -d '{
    "markdown_content": "# 标题\n内容...",
    "template_name": "travel"
  }'
```

## 测试验证

运行测试脚本：
```bash
python scripts/test_simple_template.py
```

输出示例：
```
=== 测试模板配置加载 ===
成功加载 2 个模板:
  - travel: 旅行报告模板
    文件存在: 是
  - simple: 简洁模板
    文件存在: 是

=== 测试简单模板应用 ===
--- 测试模板: travel ---
应用成功，内容长度: 234
前15行内容:
   1: #import "../../assets/templates/travel/travel.typ": template
   2: 
   3: #show: template
   4: 
   5: = 我的旅行报告
   6: ...
```

## 总结

这个简化方案的核心思想是：**不要重新发明轮子**。

- Typst 已经有完善的模板系统，我们直接使用
- cmarker 已经输出标准 Typst 语法，我们直接使用
- 简单的字符串拼接就能实现模板应用

这样既保持了系统的简洁性，又充分利用了 Typst 生态的优势，是一个真正实用的解决方案。
