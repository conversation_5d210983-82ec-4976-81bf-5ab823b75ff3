# 鉴权完整性分析与改进方案
### 🔍 当前鉴权状态

#### ✅ 已实现的安全功能

1. **JWT 认证系统**
   - 访问令牌 (Access Token) + 刷新令牌 (Refresh Token)
   - 令牌自动过期和刷新机制
   - 基于角色的访问控制 (RBAC)

2. **前端安全**
   - HttpOnly Cookies 存储令牌
   - 自动令牌刷新
   - 路由级别的认证检查

3. **后端安全**
   - JWT 签名验证
   - 用户状态检查 (is_active)
   - 速率限制 (Rate Limiting)

#### ❌ 鉴权缺失的关键问题

### 1. **WebSocket 鉴权缺失**

**问题**：WebSocket 路由完全没有鉴权
```python
@router.websocket("/ws/compile/{session_id}")
async def websocket_compile_endpoint(websocket: WebSocket, session_id: str):
    # ❌ 没有任何鉴权检查
    await manager.connect(websocket, session_id)
```

**风险**：
- 任何人都可以连接 WebSocket
- 可以无限制地使用编译服务
- 可能导致资源滥用和 DoS 攻击

### 2. **REST API 鉴权不一致**

**问题**：部分 API 路由缺少鉴权
```python
# word_format_routes.py 中注释掉了认证
# from core.security import get_current_user, require_roles
```

### 3. **资源访问控制缺失**

**问题**：
- 没有用户级别的资源隔离
- 所有用户可以访问所有模板
- 没有使用配额限制

## 🔧 完整的鉴权改进方案

### 1. WebSocket 鉴权实现

```python
@router.websocket("/ws/compile/{session_id}")
async def websocket_compile_endpoint(websocket: WebSocket, session_id: str):
    try:
        # 鉴权检查
        user_id = await get_current_user_ws(websocket)
        logger.info(f"WebSocket 连接鉴权成功: user_id={user_id}, session={session_id}")

        await manager.connect(websocket, session_id, user_id)

        # 后续消息处理...
    except WebSocketException as e:
        logger.warning(f"WebSocket 鉴权失败: {e.reason}")
        await websocket.close(code=e.code, reason=e.reason)
        return
```

## 🚀 实施优先级

### 高优先级（立即实施）
1. **WebSocket 鉴权**：防止未授权访问
2. **内容安全检查**：防止恶意内容注入




## 📋 实施检查清单

- [ ] 实现 WebSocket 鉴权
- [ ] 添加内容安全验证
# 具体方案 
下面给你一套“更简单、更强”的方案思路与可直接落地的代码片段。核心理念是：**一次性把鉴权做成“单一真相来源（SSOT）+ 单一通路”**，REST 与 WebSocket 共用同一套校验、同一套 RBAC、同一套资源隔离与配额策略；对浏览器与非浏览器客户端用**最少的分支**覆盖。

# 目标

* **更简单**：一种首选携带凭证方式、一个解码函数、一个 RBAC 装饰器/依赖、一个资源作用域检查器。
* **更强**：连接前强鉴权、会话级最小权限令牌、用户/资源/消息多层限速、原点校验、结构化消息校验、审计可追溯。

---

# 方案总览

## 1) 凭证“单一通路”

* **浏览器客户端（首选）**：同域 `HttpOnly; Secure; SameSite=Strict` 的 `access_token` Cookie。
  👉 WebSocket 握手时浏览器会自动带上 Cookie，前端不需要拼 token、也不暴露 token。
* **非浏览器/跨域**：允许 `Authorization: Bearer <token>`（可选再开 `Sec-WebSocket-Protocol` 作为备选，仅为兼容旧端）。
* **禁用**：默认禁用 `?token=` 查询串，除非确有历史包袱；若启用也仅限内网，并按 IP/Origin 白名单。

> 好处：前端更简单，后端只实现一次“拿 token → 解码 → RBAC → 作用域校验”。

## 2) 连接即鉴权 + 临时会话令牌（更强最小权限）

* 握手时用 **Access Token** 验证用户身份与状态（`jti`、`is_active`、`revoked` 等）。
* 通过后，服务器**生成一次性“WS 会话令牌”**（绑定 `user_id + session_id`，只允许本路由用途、TTL 60–300s），存 Redis。
  该令牌只在服务器内使用（无需发给前端），服务端在每条消息处理时校验该会话上下文，达到**最小权限**与**最短暴露面**（避免长期把通用 JWT 暴露在 JS 中或日志里）。

## 3) **一个**统一的解码/校验函数 + **一个**RBAC 依赖

* HTTP 与 WS 都走 `decode_and_validate_token()`；RBAC 用 `require_roles(*roles)`；资源作用域用 `enforce_scope(resource_owner_id == user.id or user.is_admin)`。

## 4) 资源隔离 & 配额

* 每个资源（如模板、编译 session）都带 `owner_id/workspace_id` 字段，服务端查询必须过滤：`WHERE owner_id=:uid OR workspace_id IN (:uids_workspaces)`。
* 配额/限速层次：**IP 级**、**user 级**、**资源级**、**消息级**（字节数、QPS、并发连接数）。

## 5) 安全硬化

* 强制 **WSS**、严格 **Origin** 校验（仅允许你的前端域名），拒绝跨站 WS。
* **消息结构化**（Pydantic）+ **最大消息/累计字节**限制 + **心跳超时** + **异常分级码**（4401/4403/1008 等）。
* **审计日志**：连接/认证/拒绝/限速触发点全记录（含 `user_id, session_id, ip, reason`）。
* **令牌吊销与轮换**：Access 短时（如 5–15 分钟）、Refresh 长时并做轮换检测；`jti` 存 Redis 允许/黑名单。

---

# 关键实现（FastAPI/Starlette 示例）

## 统一鉴权工具

```python
# core/auth.py
from fastapi import WebSocket, Header, Cookie
from jose import jwt, JWTError
from starlette.websockets import WebSocketState, WebSocketDisconnect, WebSocketClose
from starlette.websockets import WebSocket as StarletteWS
from starlette.websockets import WebSocketException
from typing import Optional
import redis.asyncio as redis
import time

REDIS = redis.from_url("redis://localhost/0", encoding="utf-8", decode_responses=True)

class Settings:
    JWT_PUBLIC_KEY = "..."         # 或 HS256 的 secret
    JWT_ALG = "RS256"
    ACCESS_TTL = 900               # 15 min
    WS_SESSION_TTL = 180           # 3 min
    ORIGINS = {"https://app.example.com"}

settings = Settings()

async def decode_and_validate_token(raw: str):
    try:
        payload = jwt.decode(raw, settings.JWT_PUBLIC_KEY, algorithms=[settings.JWT_ALG])
    except JWTError:
        raise WebSocketException(code=4401, reason="Invalid or expired token")

    # 基础校验
    if payload.get("type") != "access":
        raise WebSocketException(code=4401, reason="Wrong token type")
    if not payload.get("sub") or not payload.get("jti"):
        raise WebSocketException(code=4401, reason="Malformed token")

    # jti 是否被吊销 / 是否在允许列表
    jti = payload["jti"]
    if await REDIS.sismember("jwt:revoked", jti):
        raise WebSocketException(code=4401, reason="Token revoked")

    # 用户状态检查（如 is_active）——这里按需查询 DB
    # user = await get_user(payload["sub"])
    # if not user or not user.is_active: ...
    return payload  # {sub, roles, workspace_ids, jti, ...}

def check_origin(scope):
    origin = None
    for k, v in scope.get("headers", []):
        if k == b'origin':
            origin = v.decode()
            break
    if origin not in settings.ORIGINS:
        raise WebSocketException(code=4403, reason="Origin not allowed")

async def extract_token_for_ws(
    websocket: WebSocket,
    authorization: Optional[str] = Header(None),
    access_cookie: Optional[str] = Cookie(None, alias="access_token"),
    sec_ws_protocol: Optional[str] = Header(None, alias="sec-websocket-protocol"),
):
    # 优先 Cookie（浏览器最安全最简单）
    if access_cookie:
        return access_cookie

    # 其次 Authorization: Bearer
    if authorization and authorization.startswith("Bearer "):
        return authorization.split(" ", 1)[1].strip()

    # 兼容 subprotocol（可选打开）
    if sec_ws_protocol:
        parts = [p.strip() for p in sec_ws_protocol.split(",")]
        for p in parts:
            if p.startswith("base64.websocket.bearer."):
                import base64
                token = base64.urlsafe_b64decode(p.split(".", 3)[-1]).decode()
                return token

    # 可选：最后再考虑 ?token=（默认不支持）
    raise WebSocketException(code=4401, reason="Missing credentials")
```

## WebSocket 端点（连接即鉴权 + 会话令牌）

```python
# routes/ws.py
from fastapi import APIRouter, WebSocket
from starlette.websockets import WebSocketDisconnect, WebSocketState
from pydantic import BaseModel, Field, ValidationError, constr
import json, time, asyncio

router = APIRouter()

class CompileMessage(BaseModel):
    type: constr(strip_whitespace=True) = Field(regex="^(compile|ping)$")
    code: Optional[str] = Field(default=None, max_length=20000)
    lang: Optional[str] = Field(default=None)

async def grant_ws_session(user_id: str, session_id: str):
    key = f"wss:{user_id}:{session_id}"
    await REDIS.set(key, "1", ex=settings.WS_SESSION_TTL)
    return key

async def ensure_ws_session(user_id: str, session_id: str):
    exists = await REDIS.get(f"wss:{user_id}:{session_id}")
    if not exists:
        raise WebSocketException(code=4401, reason="WS session expired/invalid")

# 可选：简单令牌桶限速：每用户每分钟 60 次
async def allow_action(user_id: str, bucket: str, limit=60, window=60):
    now = int(time.time())
    key = f"rl:{bucket}:{user_id}:{now//window}"
    cnt = await REDIS.incr(key)
    if cnt == 1:
        await REDIS.expire(key, window)
    return cnt <= limit

@router.websocket("/ws/compile/{session_id}")
async def websocket_compile(websocket: WebSocket, session_id: str):
    try:
        # 1) 先校验 Origin（更强）
        check_origin(websocket.scope)

        # 2) 抽取 token 并解码（更简单：一次函数覆盖所有来源）
        raw = await extract_token_for_ws(websocket)
        payload = await decode_and_validate_token(raw)
        user_id = payload["sub"]

        # 3) 资源作用域：该 session 是否属于该用户/工作区
        # if not await can_access_session(user_id, session_id): ...
        # 4) 限制并发连接：同一用户同一路由最多 N 条
        # if not await try_acquire_conn(user_id): ...

        # 5) 通过后再 accept
        await websocket.accept()

        # 6) 签发/登记一次性 WS 会话
        await grant_ws_session(user_id, session_id)
        await websocket.send_json({"type": "auth_ok", "user_id": user_id})

        # 7) 消息循环：结构化校验 + 限速 + 会话有效性
        while True:
            raw_msg = await websocket.receive_text()
            try:
                msg = CompileMessage.model_validate_json(raw_msg)
            except ValidationError as e:
                await websocket.send_json({"type": "error", "reason": "invalid_message"})
                continue

            # 确保会话仍有效（最小权限、短 TTL）
            await ensure_ws_session(user_id, session_id)

            # 限速（用户级/资源级）
            if not await allow_action(user_id, "compile_messages", limit=120, window=60):
                await websocket.close(code=4403, reason="Rate limit exceeded")
                break

            if msg.type == "ping":
                await websocket.send_json({"type": "pong"})
                continue

            # 业务处理（编译）
            # result = await compile_code(msg.code, msg.lang, user_id, session_id)
            await websocket.send_json({"type": "compile_ok", "session_id": session_id})
    except WebSocketDisconnect:
        # 审计：断开
        return
    except WebSocketException as e:
        try:
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.close(code=e.code, reason=e.reason)
        finally:
            return
```

> 对比你同事的方案：我们**不要求前端显式传 token**（浏览器场景），减少代码与暴露面；后端只有 **一个**取 token 的入口函数、**一个**解码校验；**连接成功后切换到短命一次性 WS 会话**，避免长期复用通用 Access Token；再叠加 Origin 校验 + 多层限速 + 结构化消息校验，安全性显著更强。

---

## REST 与 WS 完整一致的 RBAC/作用域

```python
# core/rbac.py
from fastapi import Depends, HTTPException, status

def require_roles(*roles):
    def checker(user=Depends(get_current_user_http)):  # HTTP 用同源 Cookie/Authorization
        if not set(roles).issubset(set(user.roles)):
            raise HTTPException(status_code=403, detail="Forbidden")
        return user
    return checker

def enforce_scope(user, owner_id=None, workspace_id=None):
    if "admin" in user.roles:
        return
    if owner_id and owner_id != user.id:
        raise HTTPException(403, "Cross-user access denied")
    if workspace_id and workspace_id not in user.workspace_ids:
        raise HTTPException(403, "Cross-workspace access denied")
```

> 所有 REST/WS 的资源访问统一走 `enforce_scope`，避免“有人忘记加鉴权”的不一致问题。

---

## 配额/限速（用户/资源/消息）

* 连接数：`conn:<user_id>:<route>` 计数，不超过 N。
* 消息频率：见 `allow_action` 示例。
* 资源开销（编译任务）：以 `credits:<user_id>` 扣点或队列长度限制，防 DoS。

---

## 其他硬化点（开箱即用）

* **最大消息大小**（例如 ≤ 256 KB），超限直接 1009 关闭。
* **心跳/空闲**超时（例如 30s 未收/发 ping 直接断开）。
* **错误分级码**：
  4401 未认证 / 4403 限制 / 1008 违反策略 / 1011 服务器错误。
* **日志与审计**：连接、拒绝原因、限速触发、异常堆栈、编译统计。

---

# 实施优先级（与你的清单对齐）

1. **WebSocket 鉴权统一化**（Cookie/Authorization → 单函数 → 解码 → accept）：替代 `?token=` 方案。
2. **Origin 校验 + WSS 强制**。
3. **一次性 WS 会话令牌 + 会话续期**（服务器内部 TTL 刷新，非暴露给客户端）。
4. **RBAC/作用域统一依赖**（REST/WS 共用）。
5. **多层限速/配额**（用户/资源/消息）。
6. **结构化消息校验 + 大小限制 + 心跳**。

---

# 对比总结：为什么这套**更简单更强**

* **更少前端代码**：浏览器不需要拼 token；同域 Cookie 即可完成 WS 鉴权。
* **更少后端分支**：一个 token 提取函数、一个解码函数、一个 RBAC & 作用域检查器，HTTP/WS 复用。
* **更小攻击面**：不鼓励查询串 token；握手后切换到**短命会话**，即便被截获也价值有限。
* **更强防护层次**：Origin、限速、结构化校验、尺寸/频率/并发多重闸门。
* **更易审计与治理**：统一日志字段、统一返回码、统一拒绝原因。

如果你愿意，我可以按这套方案把你现有的 `websocket_compile_endpoint` 和相关依赖改写成可直接合并的 PR 版本（含最小可运行的 Redis mock 与测试用例）。
